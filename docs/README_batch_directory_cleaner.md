# 批量目录清理工具

## 功能描述

这个工具可以批量清理Android设备上的指定目录文件，支持预设配置、自定义配置文件等多种使用方式，帮助快速清理测试过程中产生的临时文件。

## 主要功能

1. **批量清理多个目录**：一次性清理多个指定目录
2. **预设配置**：提供常用的目录清理配置
3. **自定义配置**：支持通过JSON配置文件自定义清理目录
4. **文件类型过滤**：支持按文件类型进行清理
5. **详细日志**：提供清理过程的详细日志和统计信息

## 新增方法

### FilePusher类新增方法

#### `batch_clean_directories(directories=None, file_pattern="*")`
批量清理多个目录中的文件

**参数：**
- `directories`: 目录配置列表，每个元素为字典，包含path和description字段
- `file_pattern`: 文件匹配模式，默认为"*"（所有文件）

**返回值：**
```python
{
    "success": True,
    "total": 4,
    "success_count": 4,
    "failed_count": 0,
    "success_dirs": ["/sdcard/Download", "/sdcard/DCIM/Camera"],
    "failed_dirs": [],
    "details": [...]
}
```

#### 便捷函数
- `batch_clean_common_directories(file_pattern="*")`: 清理常用目录
- `batch_clean_custom_directories(directories, file_pattern="*")`: 清理自定义目录

## 预设配置

### common（常用目录）
- 相机拍照目录: `/sdcard/DCIM/Camera`
- 截图目录: `/sdcard/Pictures/Screenshot`
- 录屏目录: `/sdcard/Movies/ScreenRecord`
- 下载目录: `/sdcard/Download`

### media（媒体目录）
- 相机拍照目录: `/sdcard/DCIM/Camera`
- 图片目录: `/sdcard/Pictures`
- 视频目录: `/sdcard/Movies`
- 音乐目录: `/sdcard/Music`

### cache（缓存目录）
- 应用数据目录: `/sdcard/Android/data`
- 临时文件目录: `/data/local/tmp`

### test（测试目录）
- 下载目录: `/sdcard/Download`
- 相机目录: `/sdcard/DCIM/Camera`

## 使用方法

### 1. 命令行工具使用

#### 基本用法
```bash
# 清理常用目录
python tools/batch_directory_cleaner.py --preset common

# 清理媒体目录
python tools/batch_directory_cleaner.py --preset media

# 清理特定文件类型
python tools/batch_directory_cleaner.py --preset common --pattern "*.jpg"

# 使用配置文件
python tools/batch_directory_cleaner.py --config my_config.json

# 列出所有预设配置
python tools/batch_directory_cleaner.py --list-presets

# 生成示例配置文件
python tools/batch_directory_cleaner.py --generate-config
```

#### Windows批处理脚本
```bash
# 双击运行或命令行执行
tools/run_batch_cleaner.bat
```

### 2. Python代码中使用

#### 使用FilePusher类
```python
from tools.file_pusher import FilePusher

pusher = FilePusher()

# 批量清理常用目录
result = pusher.batch_clean_directories()
print(f"清理结果: 成功 {result['success_count']}/{result['total']} 个目录")

# 清理特定文件类型
result = pusher.batch_clean_directories(file_pattern="*.png")

# 自定义目录清理
custom_dirs = [
    {"path": "/sdcard/Music", "description": "音乐目录"},
    {"path": "/sdcard/Documents", "description": "文档目录"}
]
result = pusher.batch_clean_directories(custom_dirs)
```

#### 使用便捷函数
```python
from tools.file_pusher import batch_clean_common_directories, batch_clean_custom_directories

# 清理常用目录
result = batch_clean_common_directories()

# 清理自定义目录
custom_dirs = [{"path": "/sdcard/Test", "description": "测试目录"}]
result = batch_clean_custom_directories(custom_dirs)
```

#### 使用BatchDirectoryCleaner类
```python
from tools.batch_directory_cleaner import BatchDirectoryCleaner

cleaner = BatchDirectoryCleaner()

# 使用预设配置
result = cleaner.clean_preset_directories("common")

# 使用配置文件
result = cleaner.clean_from_config_file("my_config.json")

# 检查设备状态
if cleaner.check_device_status():
    print("设备已连接")
```

## 配置文件格式

### JSON配置文件示例
```json
{
  "description": "批量目录清理配置文件示例",
  "directories": [
    {
      "path": "/sdcard/Download",
      "description": "下载目录"
    },
    {
      "path": "/sdcard/DCIM/Camera",
      "description": "相机拍照目录"
    },
    {
      "path": "/sdcard/Pictures/Screenshot",
      "description": "截图目录"
    }
  ]
}
```

### 配置字段说明
- `description`: 配置文件描述（可选）
- `directories`: 目录列表（必需）
  - `path`: 目录路径（必需）
  - `description`: 目录描述（可选，用于日志显示）

## 文件匹配模式

支持标准的shell通配符模式：
- `*`: 匹配所有文件
- `*.jpg`: 匹配所有JPG文件
- `*.png`: 匹配所有PNG文件
- `test_*`: 匹配以"test_"开头的文件
- `*_temp.*`: 匹配以"_temp."结尾的文件

## 输出结果

### 成功示例
```
✅ 清理完成: 成功 4/4 个目录
```

### 部分失败示例
```
✅ 清理完成: 成功 3/4 个目录
❌ 失败的目录: /sdcard/restricted_folder
```

## 安全特性

1. **设备连接检查**：执行前自动检查设备连接状态
2. **目录验证**：验证目录配置格式的正确性
3. **错误处理**：完善的异常处理和错误提示
4. **详细日志**：记录每个操作的详细信息
5. **非破坏性**：只删除文件，不删除目录结构

## 依赖要求

- Python 3.6+
- ADB工具已安装并在PATH中
- Android设备已连接并启用USB调试

## 注意事项

1. 确保Android设备已连接并启用USB调试
2. 某些系统目录可能需要root权限才能清理
3. 清理操作不可逆，请谨慎使用
4. 建议先使用测试配置验证功能
5. 清理前可以先备份重要文件

## 故障排除

### 常见问题

1. **设备未连接**
   - 检查USB连接
   - 确认USB调试已启用
   - 运行 `adb devices` 检查设备状态

2. **权限不足**
   - 某些系统目录需要root权限
   - 尝试清理用户可访问的目录

3. **配置文件错误**
   - 检查JSON格式是否正确
   - 确认必需字段是否存在
   - 使用 `--generate-config` 生成示例配置

## 作者

AI Assistant - 2025-08-27
