# 指定应用清理功能

## 功能描述

新增了清理指定包名应用的功能，支持单个应用清理、批量应用清理和常用应用清理，提供了多种清理策略和便捷的调用方式。

## 新增方法

### AdbProcessMonitor类新增方法

#### 1. `clear_specific_app(package_name, force_stop=True, clear_data=False)`
清理指定包名的应用

**参数：**
- `package_name` (str): 应用包名，如 'com.android.chrome'
- `force_stop` (bool): 是否强制停止应用，默认True
- `clear_data` (bool): 是否清除应用数据，默认False（谨慎使用）

**返回值：**
- `bool`: 清理是否成功

**清理策略：**
1. 强制停止应用 (`am force-stop`)
2. 清除应用缓存 (`pm clear-cache`)
3. 清除应用数据 (`pm clear`) - 可选
4. 重置应用状态 (禁用后重新启用)

#### 2. `clear_multiple_apps(package_names, force_stop=True, clear_data=False)`
批量清理多个指定包名的应用

**参数：**
- `package_names` (list): 应用包名列表
- `force_stop` (bool): 是否强制停止应用，默认True
- `clear_data` (bool): 是否清除应用数据，默认False

**返回值：**
```python
{
    "success": True,
    "total": 3,
    "success_count": 2,
    "failed_count": 1,
    "success_apps": ["com.android.chrome", "com.google.android.apps.maps"],
    "failed_apps": ["com.nonexistent.app"],
    "success_rate": 0.67,
    "details": [...]
}
```

#### 3. `_check_app_exists(package_name)`
检查应用是否存在

#### 4. `_check_app_running(package_name)`
检查应用是否正在运行

## 便捷函数

### 1. `clear_app(package_name, force_stop=True, clear_data=False)`
清理指定包名应用的便捷函数

```python
from tools.adb_process_monitor import clear_app

# 清理Chrome浏览器
success = clear_app('com.android.chrome')

# 清理应用并清除数据
success = clear_app('com.example.app', clear_data=True)
```

### 2. `clear_multiple_apps(package_names, force_stop=True, clear_data=False)`
批量清理多个指定包名应用的便捷函数

```python
from tools.adb_process_monitor import clear_multiple_apps

# 批量清理多个应用
apps = ['com.android.chrome', 'com.google.android.apps.maps', 'com.facebook.katana']
result = clear_multiple_apps(apps)
print(f"清理结果: 成功 {result['success_count']}/{result['total']} 个应用")
```

### 3. `clear_common_apps(include_social=True, include_browsers=True, include_maps=True, clear_data=False)`
清理常用应用的便捷函数

```python
from tools.adb_process_monitor import clear_common_apps

# 清理所有常用应用
result = clear_common_apps()

# 只清理浏览器和地图应用
result = clear_common_apps(include_social=False)

# 清理所有常用应用并清除数据
result = clear_common_apps(clear_data=True)
```

**预定义的常用应用：**
- **浏览器**: Chrome, Edge, Firefox
- **社交**: Facebook, WhatsApp, 微信, Instagram, Twitter
- **地图**: Google Maps, 高德地图, 百度地图

## 使用示例

### 基本使用

```python
from tools.adb_process_monitor import AdbProcessMonitor

monitor = AdbProcessMonitor()

# 清理单个应用
success = monitor.clear_specific_app('com.android.chrome')
if success:
    print("✅ Chrome清理成功")
else:
    print("❌ Chrome清理失败")

# 批量清理应用
apps = [
    'com.android.chrome',
    'com.google.android.apps.maps',
    'com.facebook.katana'
]
result = monitor.clear_multiple_apps(apps)
print(f"批量清理结果: {result['success_count']}/{result['total']} 成功")
```

### 便捷函数使用

```python
from tools.adb_process_monitor import clear_app, clear_multiple_apps, clear_common_apps

# 方式1: 清理单个应用
clear_app('com.android.chrome')

# 方式2: 批量清理指定应用
apps = ['com.android.chrome', 'com.google.android.apps.maps']
result = clear_multiple_apps(apps)

# 方式3: 清理常用应用
result = clear_common_apps(include_social=True, include_browsers=True)
```

### 高级使用

```python
from tools.adb_process_monitor import AdbProcessMonitor

monitor = AdbProcessMonitor()

# 检查应用是否存在
if monitor._check_app_exists('com.android.chrome'):
    print("Chrome已安装")

# 检查应用是否运行
if monitor._check_app_running('com.android.chrome'):
    print("Chrome正在运行")

# 清理应用并清除数据（谨慎使用）
success = monitor.clear_specific_app(
    'com.example.testapp', 
    force_stop=True, 
    clear_data=True
)
```

## 清理策略详解

### 1. 强制停止应用
使用 `am force-stop` 命令强制停止应用进程

### 2. 清除应用缓存
使用 `pm clear-cache` 命令清除应用缓存文件

### 3. 清除应用数据（可选）
使用 `pm clear` 命令清除所有应用数据
⚠️ **警告**: 此操作会删除用户数据，请谨慎使用

### 4. 重置应用状态
通过临时禁用后重新启用应用来重置其状态

## 安全特性

1. **应用存在性检查**: 清理前检查应用是否已安装
2. **运行状态检测**: 检测应用当前运行状态
3. **操作结果验证**: 验证每个清理操作的结果
4. **详细日志记录**: 记录所有操作的详细信息
5. **错误处理**: 完善的异常处理机制

## 注意事项

1. **设备连接**: 确保Android设备已连接并启用USB调试
2. **权限要求**: 某些操作可能需要特殊权限
3. **数据安全**: 使用 `clear_data=True` 时要特别小心
4. **应用状态**: 清理后应用可能需要重新登录或配置
5. **系统应用**: 某些系统应用可能无法完全清理

## 错误处理

所有方法都包含完善的错误处理机制：
- 无效包名检查
- 设备连接状态验证
- ADB命令执行超时处理
- 详细的错误日志记录

## 性能优化

- 批量操作时添加适当延迟避免系统过载
- 并行检查应用状态提高效率
- 智能跳过不存在的应用
- 操作结果缓存减少重复检查

## 作者

AI Assistant - 2025-08-27
