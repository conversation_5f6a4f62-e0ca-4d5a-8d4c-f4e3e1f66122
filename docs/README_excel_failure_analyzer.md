# Excel批量挂测结果自动分析工具

## 功能描述

这个工具可以自动分析Excel中的批量挂测结果，按照失败原因进行智能归类，帮助测试团队快速识别和处理测试失败问题。

## 主要功能

1. **自动读取Excel测试报告**：支持标准的Allure测试报告Excel格式
2. **智能失败原因分析**：根据预期结果和实际结果自动判断失败类型
3. **分类统计**：按照问题类型和失败原因进行统计分析
4. **生成分析报告**：输出包含详细分析结果的Excel报告

## 分类规则

### 1. 测试状态为 "failed" 的情况
- **问题归类**：脚本问题
- **失败原因判断**：
  - 预期结果包含 `[Done, Multiple settings]` 且实际结果包含 `[Sorry, Oops, out of my reach]` → **指令支持变更：支持->不支持**
  - 预期结果包含 `[Sorry, Oops, out of my reach]` 且实际结果包含 `[Done, Multiple settings]` → **指令支持变更：不支持->支持**
  - 其他情况 → **待人工排查**
- **解决办法**：修复脚本（对于支持变更类型）
- **修复日期**：当前系统日期（YYYYMMDD格式）

### 2. 测试状态为 "error" 的情况
- **问题归类**：环境问题
- **失败原因**：环境问题
- **解决办法**：空
- **修复日期**：当前系统日期

## 使用方法

### 基本用法
```bash
python tools/excel_failure_analyzer.py
```

### 指定Excel文件路径
```bash
python tools/excel_failure_analyzer.py "D:\path\to\your\report.xlsx"
```

## 输入文件格式要求

Excel文件需要包含以下列：
- `测试状态`：测试结果状态（passed/failed/error/skipped）
- `失败消息`：包含预期结果和实际结果的详细信息
- `标签_suite`：测试套件标签
- `测试用例名称`：测试用例的名称

## 输出文件

工具会在 `failure_analysis/` 目录下生成分析报告，文件名格式：
```
failure_analysis/{原文件名}_{日期}.xlsx
```

### 输出文件包含的列：
1. **标签_suite**：测试套件
2. **问题归类**：脚本问题/环境问题
3. **失败原因**：具体的失败原因分类
4. **解决办法**：建议的解决方案
5. **修复日期**：记录的修复日期
6. **测试用例名称**：原始测试用例名称
7. **测试状态**：原始测试状态
8. **预期结果**：从失败消息中提取的预期结果
9. **实际结果**：从失败消息中提取的实际结果
10. **失败消息**：完整的失败消息

## 统计信息

工具运行后会显示：
- 总记录数
- 失败/错误用例数量
- 问题归类统计
- 具体失败原因统计

## 示例输出

```
开始分析Excel文件: D:\aigc\app_test\reports\allure_report_25-08-27_13-46-50.xlsx
成功加载Excel文件，共 480 条记录
找到 103 个失败/错误的测试用例
失败分析报告已生成: failure_analysis/failure_analysis_allure_report_25-08-27_13-46-50_20250827.xlsx
共包含 103 条失败记录

=== 失败原因统计 ===
脚本问题: 103 个

=== 具体失败原因统计 ===
指令支持变更：支持->不支持: 60 个
待人工排查: 42 个
指令支持变更：不支持->支持: 1 个

✅ 分析完成！
📊 报告文件: failure_analysis/failure_analysis_allure_report_25-08-27_13-46-50_20250827.xlsx
📈 共分析了 103 个失败测试用例
```

## 依赖要求

- Python 3.6+
- pandas
- openpyxl

## 安装依赖

```bash
pip install pandas openpyxl
```

## 注意事项

1. 确保Excel文件格式正确，包含必要的列
2. 工具会自动创建输出目录
3. 如果文件已存在，会覆盖原文件
4. 建议在运行前备份重要数据

## 作者

AI Assistant - 2025-08-27
