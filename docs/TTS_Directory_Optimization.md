# TTS目录结构优化

## 优化概述

对TTS（文本转语音）工具的文件存储目录结构进行了优化，将生成的音频文件从原来的 `data/` 目录调整到更有组织的 `data/tts/` 目录下，按语言分类存储。

## 优化内容

### 1. 目录结构调整

**优化前：**
```
data/
├── en/          # 英文TTS文件
├── zh/          # 中文TTS文件
├── ja/          # 日文TTS文件
└── ...          # 其他语言
```

**优化后：**
```
data/
├── tts/         # TTS专用目录
│   ├── en/      # 英文TTS文件
│   ├── zh/      # 中文TTS文件
│   ├── ja/      # 日文TTS文件
│   ├── ko/      # 韩文TTS文件
│   └── ...      # 其他语言
├── apk/         # APK文件
├── static/      # 静态资源
└── ...          # 其他数据
```

### 2. 代码修改

在 `utils/tts_utils.py` 中修改了数据目录路径：

```python
# 修改前
self.data_dir = self.project_root / "data"

# 修改后
self.data_dir = self.project_root / "data" / "tts"
```

## 优化优势

### 1. 更清晰的目录结构
- TTS文件与其他数据文件分离
- 避免了根数据目录的混乱
- 便于文件管理和维护

### 2. 更好的可扩展性
- 为不同类型的数据提供专门的子目录
- 支持未来添加更多数据类型
- 保持项目结构的整洁性

### 3. 更容易的文件定位
- 所有TTS相关文件集中在一个目录下
- 按语言分类，便于查找和管理
- 支持批量操作和清理

## 文件路径示例

### 英文TTS文件
```
输入: "Hello world" (en-US)
输出: data/tts/en/Hello_world.wav
```

### 中文TTS文件
```
输入: "你好世界" (zh-CN)
输出: data/tts/zh/你好世界.wav
```

### 其他语言TTS文件
```
日文: data/tts/ja/こんにちは.wav
韩文: data/tts/ko/안녕하세요.wav
法文: data/tts/fr/Bonjour.wav
德文: data/tts/de/Hallo.wav
```

## 使用方法

### 1. 基本使用（无需修改）
```python
from utils.tts_utils import generate_audio_file, speak_text

# 生成英文音频文件（自动存储到 data/tts/en/）
audio_path = generate_audio_file("Hello world", "en-US")

# 生成中文音频文件（自动存储到 data/tts/zh/）
audio_path = generate_audio_file("你好世界", "zh-CN")

# 直接朗读（使用缓存）
speak_text("Hello world", "en-US")
```

### 2. 获取文件路径
```python
from utils.tts_utils import get_audio_file_path

# 获取相对路径
relative_path = get_audio_file_path("Hello world", "en-US", relative=True)
# 返回: data/tts/en/Hello_world.wav

# 获取绝对路径
absolute_path = get_audio_file_path("Hello world", "en-US", relative=False)
# 返回: D:/aigc/app_test/data/tts/en/Hello_world.wav
```

### 3. 缓存管理
```python
from utils.tts_utils import list_cached_audio_files, clear_audio_cache, get_cache_statistics

# 列出缓存文件
cached_files = list_cached_audio_files()
# 返回按语言分组的文件列表

# 获取缓存统计
stats = get_cache_statistics()
print(f"总文件数: {stats['total_files']}")
print(f"总大小: {stats['total_size']:.1f}KB")

# 清理特定语言的缓存
clear_audio_cache(language="en", confirm=True)

# 清理所有缓存
clear_audio_cache(confirm=True)
```

## 向后兼容性

### 1. API保持不变
- 所有现有的函数接口保持不变
- 现有代码无需修改即可使用新的目录结构
- 自动处理路径转换和目录创建

### 2. 自动迁移
- 系统会自动创建新的目录结构
- 现有功能完全兼容
- 无需手动迁移数据

## 配置说明

### 1. 支持的语言代码
```python
language_mapping = {
    'zh-CN': 'zh',    # 简体中文
    'zh-TW': 'zh',    # 繁体中文
    'en-US': 'en',    # 美式英语
    'en-GB': 'en',    # 英式英语
    'ja-JP': 'ja',    # 日语
    'ko-KR': 'ko',    # 韩语
    'fr-FR': 'fr',    # 法语
    'de-DE': 'de',    # 德语
    'es-ES': 'es',    # 西班牙语
    'it-IT': 'it',    # 意大利语
    'pt-PT': 'pt',    # 葡萄牙语
    'ru-RU': 'ru',    # 俄语
}
```

### 2. 目录自动创建
- 系统会根据需要自动创建语言目录
- 支持动态添加新语言
- 确保目录权限正确

## 性能优化

### 1. 缓存机制
- 相同文本和语言的音频文件会被缓存
- 避免重复生成，提高响应速度
- 支持缓存验证和清理

### 2. 文件验证
- 自动验证音频文件的完整性
- 检查文件大小和格式
- 确保音频文件可用性

## 故障排除

### 1. 目录权限问题
```python
# 确保有足够的权限创建目录
import os
from pathlib import Path

tts_dir = Path("data/tts")
if not tts_dir.exists():
    tts_dir.mkdir(parents=True, exist_ok=True)
```

### 2. 路径分隔符问题
```python
# 系统会自动处理Windows和Unix路径分隔符
# 内部统一使用正斜杠，外部显示使用系统默认分隔符
```

### 3. 缓存文件损坏
```python
from utils.tts_utils import clear_audio_cache

# 清理损坏的缓存文件
clear_audio_cache(confirm=True)
```

## 注意事项

1. **目录权限**: 确保应用有权限在data目录下创建子目录
2. **磁盘空间**: TTS文件可能占用较多空间，定期清理缓存
3. **文件命名**: 避免使用特殊字符，系统会自动清理文件名
4. **并发访问**: 多线程环境下注意文件访问同步

## 作者

AI Assistant - 2025-08-27
