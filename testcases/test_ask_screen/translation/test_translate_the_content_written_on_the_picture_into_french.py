import pytest
import allure
from testcases.test_ask_screen.base_ask_screen_test import SimpleAskScreenTest
from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage
from core.logger import log

@allure.epic("Ella浮窗测试")
@allure.feature("Ask Screen功能")
@allure.story("翻译功能")
class TestAskScreenTranslateContentWrittenPictureIntoFrench(SimpleAskScreenTest):
    """
    Ask Screen测试类 - 翻译功能
    命令: Translate the content written on the picture into French
    图片: Translate_the_content_written_on_the_picture_into_French
    """

    @allure.title("测试Translate the content written on the picture into French")
    @allure.description("测试Ask Screen功能: Translate the content written on the picture into French")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_translate_the_content_written_on_the_picture_into_french(self, ella_floating_page):
        """测试Translate the content written on the picture into French命令"""
        command = "Translate the content written on the picture into French"
        expected_keywords = ['Comment allez-vous', 'Unable to parse this content']

        # 数据准备
        with allure.step("准备测试数据"):
            from tools.gallery_cleaner import cleaner
            # 删除已有图片
            cleaner.quick_clear_all()

            from tools.file_pusher import file_pusher
            # 推送测试图片到设备
            push_result = file_pusher.push_ask_screen_image("Translate_the_content_written_on_the_picture_into_French")
            assert push_result, f"推送图片失败: Translate_the_content_written_on_the_picture_into_French"

            # 打开图库并选择图片
            photos_page = AiGalleryPhotosPage()
            result = photos_page.start_app()
            if result:
                photos_page.wait_for_page_load()
                photos_page.click_photo()

        # 执行命令并验证
        with allure.step(f"执行Ask Screen命令: {command}"):
            success, response_texts, verification_result = self.simple_floating_command_test(
                ella_floating_page, command, expected_keywords, verify_response=True
            )

        # 断言结果
        with allure.step("验证测试结果"):
            assert success, f"命令执行失败: {command}"
            assert response_texts, "未获取到响应文本"

            # 验证响应包含期望关键词（至少匹配一个）
            self.assert_floating_response_contains(response_texts, expected_keywords, match_all=False)

        with allure.step("记录测试完成"):
            log.info(f"✅ Ask Screen测试完成: {command}")
