"""
ADB进程监控工具
通过adb命令获取当前手机所有启动中的前台、后台应用进程
"""
import argparse
import json
import os
import re
import subprocess
import sys
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import threading

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from core.logger import log
    from pages.base.detectors.detector_utils import DetectorUtils
except ImportError:
    # 如果导入失败，使用简单的日志输出
    class SimpleLogger:
        def info(self, msg): print(f"[INFO] {msg}")

        def error(self, msg): print(f"[ERROR] {msg}")

        def warning(self, msg): print(f"[WARNING] {msg}")

        def debug(self, msg): print(f"[DEBUG] {msg}")


    log = SimpleLogger()


    # 简单的ADB命令执行器
    class SimpleDetectorUtils:
        @staticmethod
        def execute_adb_command(command: List[str], timeout: int = 10) -> Tuple[bool, str]:
            try:
                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    timeout=timeout,
                    encoding='utf-8',
                    errors='ignore'
                )
                success = result.returncode == 0
                output = result.stdout if success else result.stderr
                return success, output
            except subprocess.TimeoutExpired:
                return False, "命令超时"
            except Exception as e:
                return False, str(e)


    DetectorUtils = SimpleDetectorUtils


class AdbProcessMonitor:
    """ADB进程监控器"""

    def __init__(self, cache_duration: int = 5):
        """
        初始化进程监控器

        Args:
            cache_duration: 缓存持续时间（秒），默认5秒
        """
        self.detector_utils = DetectorUtils()

        # 缓存相关属性
        self.cache_duration = cache_duration
        self._process_cache = None
        self._cache_timestamp = None
        self._cache_lock = threading.Lock()

        # 项目根目录
        self._project_root = Path(__file__).parent.parent

        # 配置缓存
        self._process_cleanup_config = None

    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if self._process_cache is None or self._cache_timestamp is None:
            return False

        elapsed = datetime.now() - self._cache_timestamp
        return elapsed.total_seconds() < self.cache_duration

    def _update_cache(self, processes: List[Dict[str, Any]]) -> None:
        """更新缓存"""
        with self._cache_lock:
            self._process_cache = processes
            self._cache_timestamp = datetime.now()

    def clear_cache(self) -> None:
        """清除缓存，强制下次获取最新数据"""
        with self._cache_lock:
            self._process_cache = None
            self._cache_timestamp = None
            log.debug("进程缓存已清除")

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        with self._cache_lock:
            if self._cache_timestamp is None:
                return {"cached": False, "cache_age": None, "cache_size": 0}

            cache_age = (datetime.now() - self._cache_timestamp).total_seconds()
            cache_size = len(self._process_cache) if self._process_cache else 0

            return {
                "cached": True,
                "cache_age": cache_age,
                "cache_duration": self.cache_duration,
                "cache_valid": self._is_cache_valid(),
                "cache_size": cache_size
            }

    def get_all_processes(self, use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        获取所有进程信息

        Args:
            use_cache: 是否使用缓存，默认True

        Returns:
            List[Dict[str, Any]]: 进程信息列表
        """
        # 如果使用缓存且缓存有效，直接返回缓存数据
        if use_cache and self._is_cache_valid():
            log.debug(f"使用缓存的进程数据 (缓存时间: {self.cache_duration}秒)")
            return self._process_cache.copy()

        try:
            log.debug("获取最新的进程数据...")
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "ps", "-A"], timeout=30
            )

            if not success:
                log.error(f"获取进程列表失败: {output}")
                return []

            processes = []
            lines = output.strip().split('\n')

            # 跳过标题行
            if len(lines) > 1:
                for line in lines[1:]:
                    process_info = self._parse_process_line(line)
                    if process_info:
                        processes.append(process_info)

            log.info(f"获取到 {len(processes)} 个进程")

            # 更新缓存
            if use_cache:
                self._update_cache(processes)

            return processes

        except Exception as e:
            log.error(f"获取进程信息失败: {e}")
            return []

    def _parse_process_line(self, line: str) -> Optional[Dict[str, Any]]:
        """
        解析进程行信息
        
        Args:
            line: 进程信息行
            
        Returns:
            Optional[Dict[str, Any]]: 解析后的进程信息
        """
        try:
            # ps -A 输出格式: USER PID PPID VSZ RSS WCHAN ADDR S NAME
            parts = line.strip().split()
            if len(parts) >= 9:
                return {
                    'user': parts[0],
                    'pid': parts[1],
                    'ppid': parts[2],
                    'vsz': parts[3],  # 虚拟内存大小
                    'rss': parts[4],  # 物理内存大小
                    'wchan': parts[5],
                    'addr': parts[6],
                    'state': parts[7],
                    'name': ' '.join(parts[8:]),
                    'type': 'unknown'
                }
        except Exception as e:
            log.debug(f"解析进程行失败: {line}, 错误: {e}")
        return None

    def get_foreground_activities(self) -> List[Dict[str, Any]]:
        """
        获取前台Activity信息
        
        Returns:
            List[Dict[str, Any]]: 前台Activity列表
        """
        try:
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "activities"], timeout=30
            )

            if not success:
                log.error(f"获取前台Activity失败: {output}")
                return []

            activities = []
            lines = output.split('\n')

            current_activity = None
            for line in lines:
                # 查找当前前台Activity
                if "mResumedActivity" in line or "mFocusedActivity" in line:
                    activity_match = re.search(r'ActivityRecord{[^}]*}\s+([^\s]+)', line)
                    if activity_match:
                        activity_name = activity_match.group(1)
                        package_name = activity_name.split('/')[0] if '/' in activity_name else activity_name

                        activities.append({
                            'activity': activity_name,
                            'package': package_name,
                            'type': 'foreground',
                            'status': 'resumed' if 'mResumedActivity' in line else 'focused'
                        })

            log.info(f"获取到 {len(activities)} 个前台Activity")
            return activities

        except Exception as e:
            log.error(f"获取前台Activity失败: {e}")
            return []

    def get_app_processes(self, processes: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        分类应用进程为前台和后台
        
        Args:
            processes: 所有进程列表
            
        Returns:
            Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]: (前台进程, 后台进程)
        """
        foreground_activities = self.get_foreground_activities()
        foreground_packages = {activity['package'] for activity in foreground_activities}

        app_processes = []
        system_processes = []

        for process in processes:
            process_name = process['name']

            # 过滤掉系统进程，只保留应用进程
            if (process_name.startswith('com.') or
                    process_name.startswith('org.') or
                    '.' in process_name and not process_name.startswith('[')):

                # 判断是否为前台进程
                is_foreground = any(pkg in process_name for pkg in foreground_packages)
                process['type'] = 'foreground' if is_foreground else 'background'
                app_processes.append(process)
            else:
                system_processes.append(process)

        foreground_processes = [p for p in app_processes if p['type'] == 'foreground']
        background_processes = [p for p in app_processes if p['type'] == 'background']

        log.info(f"应用进程分类: 前台 {len(foreground_processes)} 个, 后台 {len(background_processes)} 个")
        return foreground_processes, background_processes

    def get_process_memory_info(self, package_name: str) -> Dict[str, Any]:
        """
        获取指定包名的内存信息

        Args:
            package_name: 应用包名

        Returns:
            Dict[str, Any]: 内存信息
        """
        try:
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "meminfo", package_name], timeout=15
            )

            if not success:
                return {}

            memory_info = {}
            lines = output.split('\n')

            for line in lines:
                line = line.strip()

                # 查找 TOTAL PSS: 数值 格式
                if 'TOTAL PSS:' in line:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part == 'PSS:' and i + 1 < len(parts):
                            try:
                                pss_kb = int(parts[i + 1])
                                memory_info['pss_memory_kb'] = pss_kb
                                memory_info['pss_memory_mb'] = round(pss_kb / 1024, 2)
                            except ValueError:
                                pass
                            break

                # 查找 TOTAL RSS: 数值 格式
                elif 'TOTAL RSS:' in line:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part == 'RSS:' and i + 1 < len(parts):
                            try:
                                rss_kb = int(parts[i + 1])
                                memory_info['rss_memory_kb'] = rss_kb
                                memory_info['rss_memory_mb'] = round(rss_kb / 1024, 2)
                            except ValueError:
                                pass
                            break

                # 查找传统的 TOTAL 行格式（兼容性）
                elif 'TOTAL' in line and any(unit in line for unit in ['kB', 'KB', 'kb']):
                    parts = line.split()
                    if len(parts) >= 2:
                        try:
                            # 尝试找到第一个数字
                            for part in parts[1:]:
                                if part.isdigit():
                                    total_kb = int(part)
                                    memory_info['total_memory_kb'] = total_kb
                                    memory_info['total_memory_mb'] = round(total_kb / 1024, 2)
                                    break
                        except ValueError:
                            pass

            # 如果找到了PSS内存，优先使用PSS作为主要内存指标
            if 'pss_memory_mb' in memory_info:
                memory_info['total_memory_mb'] = memory_info['pss_memory_mb']
                memory_info['total_memory_kb'] = memory_info['pss_memory_kb']

            return memory_info

        except Exception as e:
            log.debug(f"获取内存信息失败 {package_name}: {e}")
            return {}

    def format_process_info(self, processes: List[Dict[str, Any]], process_type: str) -> str:
        """
        格式化进程信息输出
        
        Args:
            processes: 进程列表
            process_type: 进程类型 (foreground/background)
            
        Returns:
            str: 格式化后的输出
        """
        if not processes:
            return f"\n📱 {process_type.upper()} 进程: 无"

        output = f"\n📱 {process_type.upper()} 进程 ({len(processes)} 个):\n"
        output += "=" * 80 + "\n"
        output += f"{'序号':<4} {'PID':<8} {'包名/进程名':<40} {'内存(MB)':<10} {'状态':<6}\n"
        output += "-" * 80 + "\n"

        for i, process in enumerate(processes, 1):
            pid = process['pid']
            name = process['name']
            state = process['state']

            # 获取内存信息
            memory_mb = "N/A"
            if '.' in name and not name.startswith('['):
                memory_info = self.get_process_memory_info(name)
                if memory_info.get('total_memory_mb'):
                    memory_mb = str(memory_info['total_memory_mb'])

            # 截断过长的包名
            display_name = name[:38] + ".." if len(name) > 40 else name

            output += f"{i:<4} {pid:<8} {display_name:<40} {memory_mb:<10} {state:<6}\n"

        return output

    def _get_output_path(self, filename: str) -> Path:
        """
        获取输出文件的完整路径，确保存储在output目录中

        Args:
            filename: 文件名

        Returns:
            Path: 完整的输出路径
        """
        # 获取当前脚本所在目录
        current_dir = Path(__file__).parent
        output_dir = current_dir / "output"

        # 创建output目录（如果不存在）
        output_dir.mkdir(exist_ok=True)

        # 如果filename已经包含路径，只取文件名部分
        filename_only = Path(filename).name

        return output_dir / filename_only

    def export_to_json(self, foreground_processes: List[Dict[str, Any]],
                       background_processes: List[Dict[str, Any]],
                       output_file: str) -> bool:
        """
        导出进程信息到JSON文件

        Args:
            foreground_processes: 前台进程列表
            background_processes: 后台进程列表
            output_file: 输出文件名（将自动存储到output目录）

        Returns:
            bool: 导出是否成功
        """
        try:
            export_data = {
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'foreground_count': len(foreground_processes),
                    'background_count': len(background_processes),
                    'total_count': len(foreground_processes) + len(background_processes)
                },
                'foreground_processes': foreground_processes,
                'background_processes': background_processes
            }

            # 使用优化后的路径获取方法
            output_path = self._get_output_path(output_file)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            log.info(f"进程信息已导出到: {output_path}")
            return True

        except Exception as e:
            log.error(f"导出进程信息失败: {e}")
            return False

    def filter_processes(self, processes: List[Dict[str, Any]],
                         keyword: str = None,
                         min_memory_mb: float = None) -> List[Dict[str, Any]]:
        """
        过滤进程
        
        Args:
            processes: 进程列表
            keyword: 关键词过滤
            min_memory_mb: 最小内存过滤(MB)
            
        Returns:
            List[Dict[str, Any]]: 过滤后的进程列表
        """
        filtered = processes

        if keyword:
            filtered = [p for p in filtered if keyword.lower() in p['name'].lower()]

        if min_memory_mb:
            filtered_with_memory = []
            for process in filtered:
                memory_info = self.get_process_memory_info(process['name'])
                if memory_info.get('total_memory_mb', 0) >= min_memory_mb:
                    filtered_with_memory.append(process)
            filtered = filtered_with_memory

        return filtered

    def check_package_status(self, package_name: str) -> Dict[str, Any]:
        """
        检测指定包名是否在前台或后台运行

        Args:
            package_name: 应用包名

        Returns:
            Dict[str, Any]: 包含状态信息的字典
        """
        try:
            # 获取所有进程
            all_processes = self.get_all_processes()
            if not all_processes:
                return {
                    'package_name': package_name,
                    'is_running': False,
                    'is_foreground': False,
                    'is_background': False,
                    'processes': [],
                    'memory_info': {},
                    'error': '无法获取进程列表'
                }

            # 分类前台和后台进程
            foreground_processes, background_processes = self.get_app_processes(all_processes)

            # 查找指定包名的进程
            package_processes = []
            is_foreground = False
            is_background = False

            # 检查前台进程
            for process in foreground_processes:
                if package_name in process['name'] or process['name'] == package_name:
                    package_processes.append(process)
                    is_foreground = True

            # 检查后台进程
            for process in background_processes:
                if package_name in process['name'] or process['name'] == package_name:
                    package_processes.append(process)
                    is_background = True

            is_running = len(package_processes) > 0

            # 获取内存信息（如果应用正在运行）
            memory_info = {}
            if is_running:
                memory_info = self.get_process_memory_info(package_name)

            # 确定主要状态
            primary_status = 'not_running'
            if is_foreground:
                primary_status = 'foreground'
            elif is_background:
                primary_status = 'background'

            return {
                'package_name': package_name,
                'is_running': is_running,
                'is_foreground': is_foreground,
                'is_background': is_background,
                'primary_status': primary_status,
                'process_count': len(package_processes),
                'processes': package_processes,
                'memory_info': memory_info,
                'error': None
            }

        except Exception as e:
            log.error(f"检测包状态失败 {package_name}: {e}")
            return {
                'package_name': package_name,
                'is_running': False,
                'is_foreground': False,
                'is_background': False,
                'processes': [],
                'memory_info': {},
                'error': str(e)
            }

    def format_package_status(self, status_info: Dict[str, Any]) -> str:
        """
        格式化包状态信息输出

        Args:
            status_info: 包状态信息

        Returns:
            str: 格式化后的输出
        """
        package_name = status_info['package_name']

        if status_info['error']:
            return f"\n❌ 检测 {package_name} 失败: {status_info['error']}"

        if not status_info['is_running']:
            return f"\n📱 应用状态: {package_name}\n❌ 未运行"

        # 状态图标
        status_icon = "🟢" if status_info['is_foreground'] else "🔵"
        status_text = status_info['primary_status'].upper()

        output = f"\n📱 应用状态: {package_name}\n"
        output += f"{status_icon} 状态: {status_text}\n"
        output += f"📊 进程数量: {status_info['process_count']} 个\n"

        # 内存信息
        memory_info = status_info['memory_info']
        if memory_info.get('total_memory_mb'):
            output += f"💾 内存使用: {memory_info['total_memory_mb']} MB\n"

        # 进程详情
        if status_info['processes']:
            output += f"\n📋 进程详情:\n"
            output += f"{'序号':<4} {'PID':<8} {'进程名':<40} {'状态':<6}\n"
            output += "-" * 60 + "\n"

            for i, process in enumerate(status_info['processes'], 1):
                pid = process['pid']
                name = process['name']
                state = process['state']
                proc_type = "前台" if process['type'] == 'foreground' else "后台"

                # 截断过长的进程名
                display_name = name[:38] + ".." if len(name) > 40 else name
                output += f"{i:<4} {pid:<8} {display_name:<40} {state}({proc_type})\n"

        return output

    def is_package_running_fast(self, package_name: str) -> bool:
        """
        快速校验指定包名是否正在运行（优化版本）
        使用更轻量级的检测方法，避免获取完整进程列表
        增加严格验证，避免误报

        Args:
            package_name: 应用包名

        Returns:
            bool: True=正在运行, False=未运行
        """
        try:
            # 方法1: 使用pidof命令快速检测
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "pidof", package_name], timeout=3
            )

            if success and output.strip():
                pids = output.strip().split()

                # 验证每个PID是否真正对应目标包名
                for pid in pids:
                    if pid.isdigit() and self._verify_pid_matches_package(pid, package_name):
                        log.debug(f"通过pidof检测到包 {package_name} 正在运行，PID: {pid}")
                        return True

                log.debug(f"pidof找到PID但验证失败，包 {package_name} 可能未真正运行")
                return False

            # 如果pidof没有找到，直接返回False
            log.debug(f"pidof未检测到包 {package_name}，判断为未运行")
            return False

        except Exception as e:
            log.debug(f"快速校验包运行状态失败 {package_name}: {e}")
            return False

    def _verify_pid_matches_package(self, pid: str, package_name: str) -> bool:
        """
        验证PID是否真正对应指定的包名

        Args:
            pid: 进程ID
            package_name: 包名

        Returns:
            bool: True=匹配, False=不匹配
        """
        try:
            # 1. 检查进程是否存在
            success, _ = self.detector_utils.execute_adb_command(
                ["adb", "shell", "test", "-d", f"/proc/{pid}"], timeout=2
            )
            if not success:
                log.debug(f"PID {pid} 对应的进程目录不存在")
                return False

            # 2. 检查进程状态（排除僵尸进程）
            success, stat_output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "cat", f"/proc/{pid}/stat"], timeout=2
            )
            if success:
                stat_parts = stat_output.split()
                if len(stat_parts) > 2:
                    state = stat_parts[2]
                    if state == 'Z':  # 僵尸进程
                        log.debug(f"PID {pid} 是僵尸进程，忽略")
                        return False

            # 3. 检查命令行是否精确匹配
            success, cmdline_output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "cat", f"/proc/{pid}/cmdline"], timeout=2
            )
            if success:
                cmdline = cmdline_output.replace('\x00', ' ').strip()
                # 精确匹配：命令行应该以包名开头或就是包名
                if cmdline == package_name or cmdline.startswith(package_name + ' '):
                    log.debug(f"PID {pid} 命令行匹配包名: {cmdline}")
                    return True
                else:
                    log.debug(f"PID {pid} 命令行不匹配: '{cmdline}' vs '{package_name}'")
                    return False

            # 4. 如果命令行检查失败，使用ps命令交叉验证
            success, ps_output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "ps", "-p", pid], timeout=2
            )
            if success and package_name in ps_output:
                lines = ps_output.strip().split('\n')
                for line in lines[1:]:  # 跳过标题行
                    if package_name in line:
                        parts = line.split()
                        if len(parts) >= 8:
                            process_name = ' '.join(parts[8:])
                            if process_name == package_name:
                                log.debug(f"PID {pid} 通过ps命令验证匹配")
                                return True

            log.debug(f"PID {pid} 验证失败，不匹配包名 {package_name}")
            return False

        except Exception as e:
            log.debug(f"验证PID {pid} 失败: {e}")
            return False

    def is_package_actively_running(self, package_name: str) -> bool:
        """
        检测应用是否在活跃运行（排除纯后台系统服务）
        支持检测前台Activity、悬浮窗、系统覆盖层等多种活跃状态

        Args:
            package_name: 应用包名

        Returns:
            bool: True=活跃运行, False=未活跃运行或仅后台服务
        """
        try:
            # 1. 检查是否有前台Activity
            success, activity_output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "activities"], timeout=5
            )

            if success:
                # 查找当前前台Activity
                lines = activity_output.split('\n')
                for line in lines:
                    if ("mResumedActivity" in line or "mFocusedActivity" in line) and package_name in line:
                        log.debug(f"检测到 {package_name} 有前台Activity")
                        return True

            # 2. 检查焦点窗口和所有可见窗口
            success, window_output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "window", "windows"], timeout=5
            )

            if success:
                lines = window_output.split('\n')
                for line in lines:
                    # 检查焦点窗口
                    if "mCurrentFocus=" in line and package_name in line and "null" not in line:
                        log.debug(f"检测到 {package_name} 有焦点窗口")
                        return True

                    # 检查可见窗口（包括悬浮窗、系统覆盖层）
                    if package_name in line and any(keyword in line for keyword in [
                        "mVisible=true", "visible=true", "mShown=true",
                        "TYPE_APPLICATION", "TYPE_SYSTEM_OVERLAY", "TYPE_SYSTEM_ALERT"
                    ]):
                        log.debug(f"检测到 {package_name} 有可见窗口")
                        return True

            # 3. 检查最近任务状态
            success, recents_output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "recents"], timeout=5
            )

            if success and package_name in recents_output:
                lines = recents_output.split('\n')
                for line in lines:
                    # 检查是否在可见任务中
                    if package_name in line and ("visible=true" in line or "mVisible=true" in line):
                        log.debug(f"检测到 {package_name} 在可见任务中")
                        return True

                    # 检查是否在前台任务栈中（不在隐藏任务中）
                    if package_name in line and "mHiddenTasks=" not in line and "Task{" in line:
                        log.debug(f"检测到 {package_name} 在前台任务栈中")
                        return True

            # 4. 检查进程状态（区分前台和后台进程）
            success, proc_output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "processes"], timeout=5
            )

            if success and package_name in proc_output:
                lines = proc_output.split('\n')
                for line in lines:
                    if package_name in line:
                        # 检查是否标记为前台进程
                        if any(keyword in line.lower() for keyword in [
                            "foreground", "visible", "top", "resumed"
                        ]):
                            log.debug(f"检测到 {package_name} 为前台进程")
                            return True

            # 5. 特殊检查：系统级应用的活跃状态
            if self._is_system_app_active(package_name):
                return True

            log.debug(f"{package_name} 未检测到活跃运行状态")
            return False

        except Exception as e:
            log.debug(f"检测活跃运行状态失败 {package_name}: {e}")
            return False

    def _is_system_app_active(self, package_name: str) -> bool:
        """
        检查系统级应用是否处于活跃状态
        针对录屏、截图等系统功能应用的特殊检测
        """
        try:
            # 对于录屏应用，检查是否有录屏相关的系统状态
            if "screenrecord" in package_name.lower():
                # 检查是否有录屏服务在运行
                success, service_output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "dumpsys", "media.camera"], timeout=3
                )

                if success and package_name in service_output:
                    log.debug(f"检测到 {package_name} 有活跃的媒体服务")
                    return True

                # 检查通知栏是否有录屏通知
                success, notification_output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "dumpsys", "notification"], timeout=3
                )

                if success and package_name in notification_output:
                    lines = notification_output.split('\n')
                    for line in lines:
                        if package_name in line and ("ongoing" in line.lower() or "persistent" in line.lower()):
                            log.debug(f"检测到 {package_name} 有持续通知")
                            return True

            return False

        except Exception as e:
            log.debug(f"系统应用活跃检查失败 {package_name}: {e}")
            return False

    def install_app_by_name(self, app_name: str, apk_search_dir: str = "data") -> Dict[str, Any]:
        """
        根据应用名称安装应用
        1. 接收app名称例如WhatsApp
        2. 检查当前app对应的包是否安装
        3. 未安装，从电脑本地目录下查找对应的apk
        4. 使用adb install命令直接安装到手机

        Args:
            app_name: 应用名称，如 "WhatsApp", "Chrome", "WeChat" 等
            apk_search_dir: APK文件搜索目录，默认为 "data"（相对于项目根目录）

        Returns:
            Dict[str, Any]: 安装结果信息
            {
                'app_name': str,           # 应用名称
                'package_name': str,       # 包名
                'already_installed': bool, # 是否已安装
                'apk_found': bool,        # 是否找到APK文件
                'apk_path': str,          # APK文件路径
                'install_success': bool,   # 安装是否成功
                'install_output': str,     # 安装输出信息
                'error': str              # 错误信息
            }
        """
        try:
            log.info(f"🔍 开始处理应用安装请求: {app_name}")

            result = {
                'app_name': app_name,
                'package_name': '',
                'already_installed': False,
                'apk_found': False,
                'apk_path': '',
                'install_success': False,
                'install_output': '',
                'error': ''
            }

            # 步骤1: 根据应用名称获取包名
            package_name = self._get_package_name_by_app_name(app_name)
            if not package_name:
                result['error'] = f"无法识别应用名称 '{app_name}' 对应的包名"
                log.error(result['error'])
                return result

            result['package_name'] = package_name
            log.info(f"📱 应用 '{app_name}' 对应包名: {package_name}")

            # 步骤2: 检查应用是否已安装
            if self._is_package_installed(package_name):
                result['already_installed'] = True
                log.info(f"✅ 应用 '{app_name}' ({package_name}) 已安装")
                return result

            log.info(f"📦 应用 '{app_name}' ({package_name}) 未安装，开始查找APK文件...")

            # 步骤3: 在电脑本地查找APK文件
            apk_path = self._find_local_apk_file(app_name, package_name, apk_search_dir)
            if not apk_path:
                result['error'] = f"在本地目录 '{apk_search_dir}' 中未找到应用 '{app_name}' 的APK文件"
                log.error(result['error'])
                return result

            result['apk_found'] = True
            result['apk_path'] = apk_path
            log.info(f"📁 找到本地APK文件: {apk_path}")

            # 步骤4: 使用adb install安装APK
            install_success, install_output = self._install_local_apk(apk_path)
            result['install_success'] = install_success
            result['install_output'] = install_output

            if install_success:
                log.info(f"🎉 应用 '{app_name}' 安装成功!")
            else:
                result['error'] = f"安装失败: {install_output}"
                log.error(f"❌ 应用 '{app_name}' 安装失败: {install_output}")

            return result

        except Exception as e:
            error_msg = f"安装应用 '{app_name}' 时发生异常: {e}"
            log.error(error_msg)
            return {
                'app_name': app_name,
                'package_name': '',
                'already_installed': False,
                'apk_found': False,
                'apk_path': '',
                'install_success': False,
                'install_output': '',
                'error': error_msg
            }

    def _get_package_name_by_app_name(self, app_name: str) -> str:
        """
        根据应用名称获取对应的包名

        Args:
            app_name: 应用名称

        Returns:
            str: 对应的包名，如果未找到返回空字符串
        """
        # 常见应用名称到包名的映射
        app_package_mapping = {
            # 社交应用
            'whatsapp': 'com.whatsapp',
            'wechat': 'com.tencent.mm',
            'telegram': 'org.telegram.messenger',
            'facebook': 'com.facebook.katana',
            'messenger': 'com.facebook.orca',
            'instagram': 'com.instagram.android',
            'twitter': 'com.twitter.android',
            'tiktok': 'com.zhiliaoapp.musically',
            'snapchat': 'com.snapchat.android',
            'linkedin': 'com.linkedin.android',
            'discord': 'com.discord',
            'skype': 'com.skype.raider',

            # 浏览器
            'chrome': 'com.android.chrome',
            'firefox': 'org.mozilla.firefox',
            'edge': 'com.microsoft.emmx',
            'opera': 'com.opera.browser',
            'safari': 'com.apple.mobilesafari',  # iOS only

            # 媒体应用
            'youtube': 'com.google.android.youtube',
            'netflix': 'com.netflix.mediaclient',
            'spotify': 'com.spotify.music',
            'amazon prime': 'com.amazon.avod.thirdpartyclient',
            'disney+': 'com.disney.disneyplus',
            'hulu': 'com.hulu.plus',
            'twitch': 'tv.twitch.android.app',
            'vlc': 'org.videolan.vlc',

            # 购物应用
            'amazon': 'com.amazon.mShop.android.shopping',
            'ebay': 'com.ebay.mobile',
            'alibaba': 'com.alibaba.intl.android.apps.poseidon',
            'taobao': 'com.taobao.taobao',
            'tmall': 'com.tmall.wireless',

            # 工具应用
            'gmail': 'com.google.android.gm',
            'outlook': 'com.microsoft.office.outlook',
            'dropbox': 'com.dropbox.android',
            'google drive': 'com.google.android.apps.docs',
            'onedrive': 'com.microsoft.skydrive',
            'zoom': 'us.zoom.videomeetings',
            'teams': 'com.microsoft.teams',
            'slack': 'com.Slack',
            'notion': 'notion.id',
            'evernote': 'com.evernote',

            # 游戏
            'pubg': 'com.tencent.ig',
            'fortnite': 'com.epicgames.fortnite',
            'minecraft': 'com.mojang.minecraftpe',
            'candy crush': 'com.king.candycrushsaga',
            'clash of clans': 'com.supercell.clashofclans',
            'pokemon go': 'com.nianticlabs.pokemongo',

            # 导航应用
            'google maps': 'com.google.android.apps.maps',
            'waze': 'com.waze',
            'uber': 'com.ubercab',
            'lyft': 'com.lyft',
            'didi': 'com.sdu.didi.psnger',

            # 金融应用
            'paypal': 'com.paypal.android.p2pmobile',
            'alipay': 'com.eg.android.AlipayGphone',
            'wechat pay': 'com.tencent.mm',  # 微信支付集成在微信中

            # 新闻应用
            'reddit': 'com.reddit.frontpage',
            'twitter': 'com.twitter.android',
            'flipboard': 'flipboard.app',

            # 办公应用
            'microsoft word': 'com.microsoft.office.word',
            'microsoft excel': 'com.microsoft.office.excel',
            'microsoft powerpoint': 'com.microsoft.office.powerpoint',
            'google docs': 'com.google.android.apps.docs.editors.docs',
            'google sheets': 'com.google.android.apps.docs.editors.sheets',
            'wps office': 'cn.wps.moffice_eng',

            # 健康应用
            'google fit': 'com.google.android.apps.fitness',
            'samsung health': 'com.sec.android.app.shealth',
            'fitbit': 'com.fitbit.FitbitMobile',

            # 相机和图片
            'camera': 'com.android.camera2',
            'gallery': 'com.android.gallery3d',
            'google photos': 'com.google.android.apps.photos',
            'adobe photoshop': 'com.adobe.psmobile',
            'snapseed': 'com.niksoftware.snapseed',

            # 系统应用
            'settings': 'com.android.settings',
            'calculator': 'com.android.calculator2',
            'calendar': 'com.android.calendar',
            'contacts': 'com.android.contacts',
            'phone': 'com.android.dialer',
            'messages': 'com.android.messaging',
            'clock': 'com.android.deskclock',
            'file manager': 'com.android.documentsui',
        }

        # 将输入的应用名称转换为小写进行匹配
        app_name_lower = app_name.lower().strip()

        # 直接匹配
        if app_name_lower in app_package_mapping:
            return app_package_mapping[app_name_lower]

        # 模糊匹配（部分匹配）
        for key, package in app_package_mapping.items():
            if app_name_lower in key or key in app_name_lower:
                log.debug(f"模糊匹配: '{app_name}' -> '{key}' -> {package}")
                return package

        # 如果没有找到匹配，尝试生成可能的包名
        possible_packages = self._generate_possible_package_names(app_name)
        for package in possible_packages:
            if self._is_package_installed(package):
                log.debug(f"通过生成包名找到已安装应用: {package}")
                return package

        log.warning(f"未找到应用 '{app_name}' 对应的包名")
        return ""

    def _generate_possible_package_names(self, app_name: str) -> List[str]:
        """
        根据应用名称生成可能的包名列表

        Args:
            app_name: 应用名称

        Returns:
            List[str]: 可能的包名列表
        """
        app_name_clean = app_name.lower().replace(' ', '').replace('-', '').replace('_', '')

        possible_packages = [
            f"com.{app_name_clean}",
            f"com.{app_name_clean}.android",
            f"com.{app_name_clean}.app",
            f"org.{app_name_clean}",
            f"net.{app_name_clean}",
            f"io.{app_name_clean}",
            f"com.{app_name_clean}.{app_name_clean}",
            f"com.android.{app_name_clean}",
            f"com.google.android.{app_name_clean}",
        ]

        return possible_packages

    def _is_package_installed(self, package_name: str) -> bool:
        """
        检查指定包名的应用是否已安装

        Args:
            package_name: 包名

        Returns:
            bool: True=已安装, False=未安装
        """
        try:
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "pm", "list", "packages", package_name], timeout=10
            )

            if success and output.strip():
                # 检查输出中是否包含完整的包名
                lines = output.strip().split('\n')
                for line in lines:
                    if line.startswith('package:') and package_name in line:
                        # 精确匹配包名
                        installed_package = line.replace('package:', '').strip()
                        if installed_package == package_name:
                            log.debug(f"包 {package_name} 已安装")
                            return True

            log.debug(f"包 {package_name} 未安装")
            return False

        except Exception as e:
            log.debug(f"检查包安装状态失败 {package_name}: {e}")
            return False

    def _find_local_apk_file(self, app_name: str, package_name: str, search_dir: str) -> str:
        """
        在电脑本地目录中查找APK文件

        Args:
            app_name: 应用名称
            package_name: 包名
            search_dir: 搜索目录（相对于项目根目录或绝对路径）

        Returns:
            str: APK文件路径，如果未找到返回空字符串
        """
        try:
            import glob

            log.info(f"🔍 在本地目录中查找应用 '{app_name}' 的APK文件...")

            # 确定搜索目录的绝对路径
            if os.path.isabs(search_dir):
                search_path = search_dir
            else:
                # 相对于项目根目录
                search_path = os.path.join(self._project_root, search_dir)

            log.info(f"📁 搜索路径: {search_path}")

            # 检查搜索目录是否存在
            if not os.path.exists(search_path):
                log.error(f"本地搜索目录不存在: {search_path}")
                return ""

            if not os.path.isdir(search_path):
                log.error(f"搜索路径不是目录: {search_path}")
                return ""

            # 生成可能的APK文件名模式
            search_patterns = self._generate_local_apk_patterns(app_name, package_name)

            # 在目录中搜索APK文件
            for pattern in search_patterns:
                log.debug(f"搜索模式: {pattern}")

                # 构建完整的搜索路径
                full_pattern = os.path.join(search_path, pattern)

                # 使用glob搜索匹配的文件
                matching_files = glob.glob(full_pattern, recursive=True)

                for apk_file in matching_files:
                    if apk_file.lower().endswith('.apk'):
                        # 验证APK文件是否有效
                        if self._validate_local_apk_file(apk_file):
                            log.info(f"✅ 找到匹配的本地APK文件: {apk_file}")
                            return apk_file

            # 如果精确搜索失败，尝试递归搜索
            log.info("精确搜索失败，尝试递归搜索...")
            return self._recursive_search_local_apk(app_name, package_name, search_path)

        except Exception as e:
            log.error(f"搜索本地APK文件时发生异常: {e}")
            return ""

    def _generate_local_apk_patterns(self, app_name: str, package_name: str) -> List[str]:
        """
        生成本地APK文件搜索模式

        Args:
            app_name: 应用名称
            package_name: 包名

        Returns:
            List[str]: 搜索模式列表
        """
        app_name_clean = app_name.lower().replace(' ', '').replace('-', '').replace('_', '')
        app_name_with_space = app_name.lower().replace(' ', '_')
        app_name_with_dash = app_name.lower().replace(' ', '-')

        # 从包名中提取可能的应用名
        package_parts = package_name.split('.')
        package_app_name = package_parts[-1] if package_parts else ""

        patterns = [
            # 直接匹配
            f"{app_name_clean}.apk",
            f"{app_name_with_space}.apk",
            f"{app_name_with_dash}.apk",
            f"{package_app_name}.apk",
            f"{app_name.lower()}.apk",

            # 模糊匹配
            f"*{app_name_clean}*.apk",
            f"*{app_name_with_space}*.apk",
            f"*{app_name_with_dash}*.apk",
            f"*{package_app_name}*.apk",
            f"*{app_name.lower()}*.apk",

            # 带版本号的匹配
            f"{app_name_clean}_*.apk",
            f"{app_name_clean}-*.apk",
            f"*{app_name_clean}*android*.apk",
            f"*{app_name_clean}*mobile*.apk",
            f"*{app_name_clean}*app*.apk",

            # 递归搜索模式
            f"**/{app_name_clean}.apk",
            f"**/*{app_name_clean}*.apk",
            f"**/{package_app_name}.apk",
            f"**/*{package_app_name}*.apk",
        ]

        # 去重并返回
        return list(set(patterns))

    def _validate_local_apk_file(self, apk_path: str) -> bool:
        """
        验证本地APK文件是否有效

        Args:
            apk_path: APK文件路径

        Returns:
            bool: True=有效, False=无效
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(apk_path):
                log.debug(f"APK文件不存在: {apk_path}")
                return False

            # 检查文件大小（基本验证）
            file_size = os.path.getsize(apk_path)
            if file_size < 1024:  # 至少1KB
                log.debug(f"APK文件太小，可能无效: {apk_path} ({file_size} bytes)")
                return False

            # 检查文件扩展名
            if not apk_path.lower().endswith('.apk'):
                log.debug(f"文件扩展名不正确: {apk_path}")
                return False

            log.debug(f"本地APK文件验证通过: {apk_path} ({file_size} bytes)")
            return True

        except Exception as e:
            log.debug(f"验证本地APK文件失败 {apk_path}: {e}")
            return False

    def _recursive_search_local_apk(self, app_name: str, package_name: str, search_path: str) -> str:
        """
        递归搜索本地APK文件

        Args:
            app_name: 应用名称
            package_name: 包名
            search_path: 搜索路径

        Returns:
            str: APK文件路径，如果未找到返回空字符串
        """
        try:
            import glob

            log.debug(f"递归搜索本地APK文件: {app_name} in {search_path}")

            # 使用更宽泛的搜索条件
            app_name_parts = app_name.lower().split()

            for part in app_name_parts:
                if len(part) >= 3:  # 只搜索长度>=3的部分，避免太宽泛
                    pattern = os.path.join(search_path, "**", f"*{part}*.apk")
                    matching_files = glob.glob(pattern, recursive=True)

                    for apk_file in matching_files:
                        if self._validate_local_apk_file(apk_file):
                            log.info(f"✅ 递归搜索找到匹配的本地APK文件: {apk_file}")
                            return apk_file

            log.warning(f"递归搜索未找到应用 '{app_name}' 的本地APK文件")
            return ""

        except Exception as e:
            log.error(f"递归搜索本地APK文件时发生异常: {e}")
            return ""

    def _install_local_apk(self, apk_path: str) -> Tuple[bool, str]:
        """
        使用adb install安装本地APK文件

        Args:
            apk_path: 本地APK文件路径

        Returns:
            Tuple[bool, str]: (安装是否成功, 安装输出信息)
        """
        try:
            log.info(f"📦 开始安装本地APK文件: {apk_path}")

            # 检查APK文件是否存在
            if not os.path.exists(apk_path):
                error_msg = f"APK文件不存在: {apk_path}"
                log.error(error_msg)
                return False, error_msg

            # 使用adb install命令安装APK
            # -r 参数表示替换已存在的应用
            # -t 参数允许安装测试APK
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "install", "-r", "-t", apk_path], timeout=120  # 安装可能需要较长时间
            )

            if success:
                # 检查安装输出
                if "Success" in output or "成功" in output:
                    log.info(f"✅ 本地APK安装成功: {apk_path}")
                    return True, output
                else:
                    log.error(f"❌ 本地APK安装失败: {output}")
                    return False, output
            else:
                log.error(f"❌ adb install命令执行失败: {output}")
                return False, output

        except Exception as e:
            error_msg = f"安装本地APK文件时发生异常: {e}"
            log.error(error_msg)
            return False, error_msg

    def _find_apk_file(self, app_name: str, package_name: str, search_dir: str) -> str:
        """
        在指定目录中查找APK文件

        Args:
            app_name: 应用名称
            package_name: 包名
            search_dir: 搜索目录

        Returns:
            str: APK文件路径，如果未找到返回空字符串
        """
        try:
            log.info(f"🔍 在Android设备目录 '{search_dir}' 中查找应用 '{app_name}' 的APK文件...")

            # 检查搜索目录是否存在（在Android设备上）
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "test", "-d", search_dir], timeout=5
            )

            if not success:
                log.warning(f"Android设备上的搜索目录 '{search_dir}' 不存在或无法访问")
                # 尝试常见的APK存储目录
                alternative_dirs = [
                    "/sdcard/Download",
                    "/sdcard/Downloads",
                    "/storage/emulated/0/Download",
                    "/storage/emulated/0/Downloads",
                    "/data/local/tmp",
                    "/sdcard"
                ]

                log.info("🔍 尝试在常见APK存储目录中搜索...")
                for alt_dir in alternative_dirs:
                    success, _ = self.detector_utils.execute_adb_command(
                        ["adb", "shell", "test", "-d", alt_dir], timeout=3
                    )
                    if success:
                        log.info(f"✅ 找到可访问的目录: {alt_dir}")
                        search_dir = alt_dir
                        break
                else:
                    log.error("❌ 未找到任何可访问的APK存储目录")
                    return ""

            # 生成可能的APK文件名模式
            search_patterns = self._generate_apk_search_patterns(app_name, package_name)

            # 在目录中搜索APK文件
            for pattern in search_patterns:
                log.debug(f"搜索模式: {pattern}")

                # 使用find命令搜索
                success, output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "find", search_dir, "-name", pattern, "-type", "f"],
                    timeout=15
                )

                if success and output.strip():
                    apk_files = output.strip().split('\n')
                    for apk_file in apk_files:
                        apk_file = apk_file.strip()
                        if apk_file and apk_file.endswith('.apk'):
                            # 验证APK文件是否有效
                            if self._validate_apk_file(apk_file, package_name):
                                log.info(f"✅ 找到匹配的APK文件: {apk_file}")
                                return apk_file

            # 如果精确搜索失败，尝试递归搜索
            log.info("精确搜索失败，尝试递归搜索...")
            return self._recursive_search_apk(app_name, package_name, search_dir)

        except Exception as e:
            log.error(f"搜索APK文件时发生异常: {e}")
            return ""

    def _generate_apk_search_patterns(self, app_name: str, package_name: str) -> List[str]:
        """
        生成APK文件搜索模式

        Args:
            app_name: 应用名称
            package_name: 包名

        Returns:
            List[str]: 搜索模式列表
        """
        app_name_clean = app_name.lower().replace(' ', '').replace('-', '').replace('_', '')
        app_name_with_space = app_name.lower().replace(' ', '_')

        # 从包名中提取可能的应用名
        package_parts = package_name.split('.')
        package_app_name = package_parts[-1] if package_parts else ""

        patterns = [
            f"*{app_name_clean}*.apk",
            f"*{app_name_with_space}*.apk",
            f"*{app_name.lower()}*.apk",
            f"*{package_app_name}*.apk",
            f"{app_name_clean}.apk",
            f"{app_name_with_space}.apk",
            f"{package_app_name}.apk",
            f"*{app_name_clean}*android*.apk",
            f"*{app_name_clean}*mobile*.apk",
            f"*{app_name_clean}*app*.apk",
        ]

        # 去重并返回
        return list(set(patterns))

    def _validate_apk_file(self, apk_path: str, expected_package: str) -> bool:
        """
        验证APK文件是否有效且包名匹配

        Args:
            apk_path: APK文件路径
            expected_package: 期望的包名

        Returns:
            bool: True=有效且匹配, False=无效或不匹配
        """
        try:
            # 检查文件是否存在
            success, _ = self.detector_utils.execute_adb_command(
                ["adb", "shell", "test", "-f", apk_path], timeout=5
            )

            if not success:
                log.debug(f"APK文件不存在: {apk_path}")
                return False

            # 使用aapt命令获取APK包信息（如果可用）
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "aapt", "dump", "badging", apk_path], timeout=10
            )

            if success and output.strip():
                # 解析包名
                lines = output.split('\n')
                for line in lines:
                    if line.startswith('package:'):
                        # 格式: package: name='com.example.app' versionCode='1' versionName='1.0'
                        if f"name='{expected_package}'" in line:
                            log.debug(f"APK文件包名匹配: {apk_path} -> {expected_package}")
                            return True
                        else:
                            log.debug(f"APK文件包名不匹配: {apk_path}")
                            return False

            # 如果aapt不可用，检查文件大小（基本验证）
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "stat", "-c", "%s", apk_path], timeout=5
            )

            if success and output.strip():
                file_size = int(output.strip())
                if file_size > 1024:  # 至少1KB
                    log.debug(f"APK文件大小验证通过: {apk_path} ({file_size} bytes)")
                    return True
                else:
                    log.debug(f"APK文件太小，可能无效: {apk_path} ({file_size} bytes)")
                    return False

            return False

        except Exception as e:
            log.debug(f"验证APK文件失败 {apk_path}: {e}")
            return False

    def _recursive_search_apk(self, app_name: str, package_name: str, search_dir: str) -> str:
        """
        递归搜索APK文件

        Args:
            app_name: 应用名称
            package_name: 包名
            search_dir: 搜索目录

        Returns:
            str: APK文件路径，如果未找到返回空字符串
        """
        try:
            log.debug(f"递归搜索APK文件: {app_name} in {search_dir}")

            # 使用更宽泛的搜索条件
            app_name_parts = app_name.lower().split()

            for part in app_name_parts:
                if len(part) >= 3:  # 只搜索长度>=3的部分，避免太宽泛
                    success, output = self.detector_utils.execute_adb_command(
                        ["adb", "shell", "find", search_dir, "-name", f"*{part}*.apk", "-type", "f"],
                        timeout=20
                    )

                    if success and output.strip():
                        apk_files = output.strip().split('\n')
                        for apk_file in apk_files:
                            apk_file = apk_file.strip()
                            if apk_file and apk_file.endswith('.apk'):
                                log.debug(f"检查候选APK文件: {apk_file}")
                                if self._validate_apk_file(apk_file, package_name):
                                    log.info(f"✅ 递归搜索找到匹配的APK文件: {apk_file}")
                                    return apk_file

            log.warning(f"递归搜索未找到应用 '{app_name}' 的APK文件")
            return ""

        except Exception as e:
            log.error(f"递归搜索APK文件时发生异常: {e}")
            return ""

    def _install_apk(self, apk_path: str) -> Tuple[bool, str]:
        """
        安装APK文件

        Args:
            apk_path: APK文件路径

        Returns:
            Tuple[bool, str]: (安装是否成功, 安装输出信息)
        """
        try:
            log.info(f"📦 开始安装APK文件: {apk_path}")

            # 使用adb install命令安装APK
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "install", "-r", apk_path], timeout=60  # 安装可能需要较长时间
            )

            if success:
                # 检查安装输出
                if "Success" in output:
                    log.info(f"✅ APK安装成功: {apk_path}")
                    return True, output
                else:
                    log.error(f"❌ APK安装失败: {output}")
                    return False, output
            else:
                log.error(f"❌ APK安装命令执行失败: {output}")
                return False, output

        except Exception as e:
            error_msg = f"安装APK文件时发生异常: {e}"
            log.error(error_msg)
            return False, error_msg

    def format_install_result(self, result: Dict[str, Any]) -> str:
        """
        格式化安装结果输出

        Args:
            result: 安装结果字典

        Returns:
            str: 格式化后的输出
        """
        app_name = result['app_name']
        package_name = result['package_name']

        output = f"\n📱 应用安装结果: {app_name}\n"
        output += "=" * 50 + "\n"

        if result['error']:
            output += f"❌ 错误: {result['error']}\n"
            return output

        if package_name:
            output += f"📦 包名: {package_name}\n"

        if result['already_installed']:
            output += f"✅ 状态: 应用已安装\n"
            return output

        if result['apk_found']:
            output += f"📁 APK路径: {result['apk_path']}\n"

            if result['install_success']:
                output += f"🎉 安装状态: 成功\n"
                output += f"📝 安装输出: {result['install_output']}\n"
            else:
                output += f"❌ 安装状态: 失败\n"
                output += f"📝 失败原因: {result['install_output']}\n"
        else:
            output += f"❌ APK文件: 未找到\n"

        return output

    def is_package_running(self, package_name: str, use_fast_method: bool = True) -> bool:
        """
        校验指定包名是否在前台或后台运行

        注意：此方法不使用缓存策略，每次都获取最新的进程状态
        因为测试脚本运行前会进行进程清理，缓存数据可能已过时

        Args:
            package_name: 应用包名
            use_fast_method: 是否使用快速检测方法，默认True

        Returns:
            bool: True=正在运行(前台或后台), False=未运行
        """
        try:
            # 优先使用快速检测方法
            if use_fast_method:
                if self.is_package_running_fast(package_name):
                    return True
            # 使用实时检测方法（不使用缓存）
            # 因为每次运行脚本前都会进行进程清理，缓存数据可能已过时
            status_info = self.check_package_status(package_name)

            # 如果有错误或检测失败，返回False
            if status_info.get('error'):
                log.debug(f"检测包状态失败 {package_name}: {status_info['error']}")
                return False

            # 返回是否正在运行（前台或后台都算运行）
            return status_info.get('is_running', False)

        except Exception as e:
            log.debug(f"校验包运行状态失败 {package_name}: {e}")
            return False

    def check_package_status_cached(self, package_name: str) -> Dict[str, Any]:
        """
        检测指定包名状态（使用缓存优化版本）

        Args:
            package_name: 应用包名

        Returns:
            Dict[str, Any]: 包含状态信息的字典
        """
        try:
            # 使用缓存的进程列表
            all_processes = self.get_all_processes(use_cache=True)
            if not all_processes:
                return {
                    'package_name': package_name,
                    'is_running': False,
                    'is_foreground': False,
                    'is_background': False,
                    'processes': [],
                    'memory_info': {},
                    'error': '无法获取进程列表'
                }

            # 快速查找指定包名的进程
            package_processes = []
            for process in all_processes:
                if package_name in process.get('name', '') or process.get('name', '') == package_name:
                    package_processes.append(process)

            is_running = len(package_processes) > 0

            return {
                'package_name': package_name,
                'is_running': is_running,
                'is_foreground': False,  # 简化版本不区分前后台
                'is_background': is_running,
                'primary_status': 'background' if is_running else 'not_running',
                'process_count': len(package_processes),
                'processes': package_processes,
                'memory_info': {},
                'error': None
            }

        except Exception as e:
            return {
                'package_name': package_name,
                'is_running': False,
                'is_foreground': False,
                'is_background': False,
                'processes': [],
                'memory_info': {},
                'error': str(e)
            }

    def _load_process_cleanup_config(self):
        """按需加载进程清理配置"""
        if self._process_cleanup_config is None:
            try:
                config_path = self._project_root / "config" / "process_cleanup_config.json"
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self._process_cleanup_config = config_data.get("process_cleanup_config", {})
                log.debug(f"✅ 已加载进程清理配置，共 {len(self._process_cleanup_config)} 项")
            except Exception as e:
                log.error(f"❌ 加载进程清理配置失败: {e}")
                self._process_cleanup_config = {
                    "common_user_apps": [],
                    "recent_apps_clear_positions": [],
                    "cleanup_settings": {}
                }
        return self._process_cleanup_config

    def _should_stop_package(self, package_name):
        """
        判断是否应该停止某个包
        """
        if not package_name:
            return False

        # 不停止系统关键应用
        system_packages = [
            "com.android.systemui",
            "android",
            "com.android.phone",
            "com.android.settings",
            "com.android.launcher",
            "com.android.inputmethod",
            "com.google.android.inputmethod"
        ]

        # 不停止当前测试相关的应用
        test_packages = [
            "com.transsion.aivoiceassistant",  # Ella
        ]

        for sys_pkg in system_packages + test_packages:
            if sys_pkg in package_name.lower():
                return False

        return True

    def _parse_recent_packages(self, dumpsys_output):
        """
        解析dumpsys输出中的Recent应用包名
        """
        packages = set()
        try:
            lines = dumpsys_output.split('\n')
            for line in lines:
                if 'Task{' in line and 'A=' in line:
                    # 解析格式如: Task{123 #456 A=com.example.app U=0 StackId=1 sz=1}
                    start = line.find('A=') + 2
                    end = line.find(' ', start)
                    if start > 1 and end > start:
                        package_name = line[start:end]
                        if package_name and '.' in package_name:
                            packages.add(package_name)
        except Exception as e:
            log.debug(f"解析Recent包名异常: {e}")

        return packages

    def clear_all_running_processes(self):
        """
        清除手机上所有运行中的应用进程
        优先使用命令直接清理，Recent页面清理作为备选方案
        在测试用例执行前调用，确保测试环境干净
        """
        try:
            log.info("🧹 开始清除手机上所有运行中的应用进程...")

            # 加载配置
            config = self._load_process_cleanup_config()
            cleanup_settings = config.get("cleanup_settings", {})

            command_cleanup_enabled = cleanup_settings.get("command_cleanup_enabled", True)
            recent_fallback_enabled = cleanup_settings.get("recent_apps_fallback_enabled", True)
            force_stop_enabled = cleanup_settings.get("force_stop_enabled", True)
            stabilization_wait = cleanup_settings.get("stabilization_wait", 2)
            min_apps_for_fallback = cleanup_settings.get("min_apps_for_fallback", 3)

            cleared_count = 0

            # 策略1: 优先使用命令直接清理（快速、可靠）
            if command_cleanup_enabled:
                log.info("⚡ 优先使用命令直接清理...")
                command_count = self._command_clear_all_apps()
                cleared_count += command_count

            # 策略2: 强制停止特定应用（针对顽固应用如Google Maps）
            if force_stop_enabled:
                log.info("💪 强制停止顽固应用...")
                force_count = self._force_stop_stubborn_apps()
                cleared_count += force_count

            # 策略3: Recent页面清理（备选方案，当命令清理效果不佳时使用）
            if recent_fallback_enabled and cleared_count < min_apps_for_fallback:
                log.info("🎯 使用Recent页面清理作为备选方案...")
                recent_count = self.clear_recent_apps()
                cleared_count += recent_count

            log.info(f"🎉 应用进程清理完成，共清理 {cleared_count} 个应用")

            # 等待系统稳定
            time.sleep(stabilization_wait)

        except Exception as e:
            log.error(f"❌ 清理应用进程失败: {e}")

    def clear_specific_app(self, package_name: str, force_stop: bool = True, clear_data: bool = False) -> bool:
        """
        清理指定包名的应用

        Args:
            package_name: 应用包名，如 'com.android.chrome'
            force_stop: 是否强制停止应用，默认True
            clear_data: 是否清除应用数据，默认False（谨慎使用）

        Returns:
            bool: 清理是否成功
        """
        try:
            log.info(f"🎯 开始清理指定应用: {package_name}")

            if not package_name or not isinstance(package_name, str):
                log.error("❌ 无效的包名")
                return False

            # 加载配置
            config = self._load_process_cleanup_config()
            cleanup_settings = config.get("cleanup_settings", {})
            timeout = cleanup_settings.get("cleanup_timeout", 8)

            success_count = 0
            total_operations = 0

            # 检查应用是否存在
            if not self._check_app_exists(package_name):
                log.warning(f"⚠️ 应用不存在或未安装: {package_name}")
                return False

            # 检查应用是否正在运行
            is_running = self._check_app_running(package_name)
            log.info(f"📱 应用运行状态: {'运行中' if is_running else '未运行'}")

            # 方法1: 强制停止应用
            if force_stop:
                total_operations += 1
                log.debug(f"🛑 强制停止应用: {package_name}")

                try:
                    success, output = self.detector_utils.execute_adb_command(
                        ["adb", "shell", "am", "force-stop", package_name],
                        timeout=timeout
                    )
                    if success:
                        success_count += 1
                        log.info(f"✅ 成功强制停止应用: {package_name}")
                    else:
                        log.warning(f"⚠️ 强制停止应用失败: {output}")
                except Exception as e:
                    log.warning(f"⚠️ 强制停止应用异常: {e}")

            # 方法2: 清除应用缓存
            total_operations += 1
            log.debug(f"🧹 清除应用缓存: {package_name}")

            try:
                success, output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "pm", "clear-cache", package_name],
                    timeout=timeout
                )
                if success:
                    success_count += 1
                    log.info(f"✅ 成功清除应用缓存: {package_name}")
                else:
                    log.debug(f"清除缓存结果: {output}")
            except Exception as e:
                log.warning(f"⚠️ 清除应用缓存异常: {e}")

            # 方法3: 清除应用数据（谨慎使用）
            if clear_data:
                total_operations += 1
                log.warning(f"⚠️ 清除应用数据: {package_name} (此操作将删除所有用户数据)")

                try:
                    success, output = self.detector_utils.execute_adb_command(
                        ["adb", "shell", "pm", "clear", package_name],
                        timeout=timeout
                    )
                    if success:
                        success_count += 1
                        log.info(f"✅ 成功清除应用数据: {package_name}")
                    else:
                        log.warning(f"⚠️ 清除应用数据失败: {output}")
                except Exception as e:
                    log.warning(f"⚠️ 清除应用数据异常: {e}")

            # 方法4: 重置应用状态（禁用后重新启用）
            total_operations += 1
            log.debug(f"🔄 重置应用状态: {package_name}")

            try:
                # 临时禁用应用
                self.detector_utils.execute_adb_command(
                    ["adb", "shell", "pm", "disable-user", package_name],
                    timeout=3
                )
                time.sleep(0.5)

                # 重新启用应用
                success, output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "pm", "enable", package_name],
                    timeout=3
                )
                if success:
                    success_count += 1
                    log.debug(f"✅ 成功重置应用状态: {package_name}")
                else:
                    log.debug(f"重置应用状态结果: {output}")
            except Exception as e:
                log.debug(f"重置应用状态异常: {e}")

            # 等待操作生效
            time.sleep(1)

            # 验证清理结果
            final_running = self._check_app_running(package_name)

            success_rate = success_count / total_operations if total_operations > 0 else 0

            if success_rate >= 0.5:  # 至少50%的操作成功
                log.info(f"✅ 应用清理完成: {package_name} (成功率: {success_rate:.1%})")
                log.info(f"📊 清理后状态: {'仍在运行' if final_running else '已停止'}")
                return True
            else:
                log.warning(f"⚠️ 应用清理部分失败: {package_name} (成功率: {success_rate:.1%})")
                return False

        except Exception as e:
            log.error(f"❌ 清理指定应用失败: {e}")
            return False

    def _check_app_exists(self, package_name: str) -> bool:
        """检查应用是否存在"""
        try:
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "pm", "list", "packages", package_name],
                timeout=5
            )
            return success and package_name in output
        except Exception as e:
            log.debug(f"检查应用存在性异常: {e}")
            return False

    def _check_app_running(self, package_name: str) -> bool:
        """检查应用是否正在运行"""
        try:
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "ps", "|", "grep", package_name],
                timeout=5
            )
            return success and package_name in output
        except Exception as e:
            log.debug(f"检查应用运行状态异常: {e}")
            return False

    def clear_multiple_apps(self, package_names: list, force_stop: bool = True, clear_data: bool = False) -> dict:
        """
        批量清理多个指定包名的应用

        Args:
            package_names: 应用包名列表，如 ['com.android.chrome', 'com.google.android.apps.maps']
            force_stop: 是否强制停止应用，默认True
            clear_data: 是否清除应用数据，默认False（谨慎使用）

        Returns:
            dict: 清理结果统计
        """
        try:
            log.info(f"🎯 开始批量清理 {len(package_names)} 个指定应用")

            if not package_names or not isinstance(package_names, list):
                log.error("❌ 无效的包名列表")
                return {"success": False, "error": "无效的包名列表"}

            results = {
                "total": len(package_names),
                "success_count": 0,
                "failed_count": 0,
                "success_apps": [],
                "failed_apps": [],
                "details": []
            }

            for package_name in package_names:
                log.info(f"📱 正在清理应用: {package_name}")

                try:
                    success = self.clear_specific_app(package_name, force_stop, clear_data)

                    detail = {
                        "package_name": package_name,
                        "success": success
                    }

                    if success:
                        results["success_count"] += 1
                        results["success_apps"].append(package_name)
                        log.info(f"✅ {package_name} 清理成功")
                    else:
                        results["failed_count"] += 1
                        results["failed_apps"].append(package_name)
                        log.warning(f"❌ {package_name} 清理失败")

                    results["details"].append(detail)

                    # 添加短暂延迟，避免操作过快
                    time.sleep(0.5)

                except Exception as e:
                    log.error(f"❌ 清理应用 {package_name} 时异常: {e}")
                    results["failed_count"] += 1
                    results["failed_apps"].append(package_name)
                    results["details"].append({
                        "package_name": package_name,
                        "success": False,
                        "error": str(e)
                    })

            # 输出总结
            success_rate = results["success_count"] / results["total"] if results["total"] > 0 else 0
            log.info(f"🎉 批量清理完成: 成功 {results['success_count']}/{results['total']} 个应用 (成功率: {success_rate:.1%})")

            if results["failed_count"] > 0:
                log.warning(f"❌ 失败的应用: {', '.join(results['failed_apps'])}")

            results["success"] = True
            results["success_rate"] = success_rate
            return results

        except Exception as e:
            log.error(f"❌ 批量清理应用失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "total": 0,
                "success_count": 0,
                "failed_count": 0
            }

    def _command_clear_all_apps(self):
        """
        使用命令直接清理所有应用进程
        这是最快速和可靠的清理方式
        """
        cleared_count = 0
        try:
            log.debug("⚡ 执行命令直接清理...")

            # 加载配置
            config = self._load_process_cleanup_config()
            cleanup_settings = config.get("cleanup_settings", {})
            timeout = cleanup_settings.get("cleanup_timeout", 8)

            # 方法1: 获取所有运行的应用包名并逐个停止
            cleared_count += self._stop_running_apps_by_list()

            # 方法2: 使用系统级清理命令
            cleared_count += self._system_level_cleanup()

            # 方法3: 清理特定类型的应用
            cleared_count += self._clear_apps_by_category()

            log.debug(f"✅ 命令清理完成，清理了 {cleared_count} 个应用")

        except Exception as e:
            log.debug(f"命令清理异常: {e}")

        return cleared_count

    def _stop_running_apps_by_list(self):
        """
        获取运行中的应用列表并逐个停止
        """
        cleared_count = 0
        try:
            log.debug("📋 获取运行应用列表并逐个停止...")

            # 获取所有运行中的应用
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "-E",
                 "mResumedActivity|mFocusedActivity"],
                timeout=10
            )

            if success and output.strip():
                lines = output.strip().split('\n')
                packages_to_stop = set()

                # 解析包名
                for line in lines:
                    if 'ComponentInfo{' in line:
                        try:
                            # 提取包名，格式如: ComponentInfo{com.example.app/com.example.app.MainActivity}
                            start = line.find('ComponentInfo{') + len('ComponentInfo{')
                            end = line.find('/', start)
                            if start > 0 and end > start:
                                package_name = line[start:end]
                                if package_name and '.' in package_name:
                                    packages_to_stop.add(package_name)
                        except:
                            continue

                # 停止找到的应用
                for package_name in packages_to_stop:
                    if self._should_stop_package(package_name):
                        try:
                            success, _ = self.detector_utils.execute_adb_command(
                                ["adb", "shell", "am", "force-stop", package_name],
                                timeout=3
                            )
                            if success:
                                cleared_count += 1
                                log.debug(f"✅ 停止运行应用: {package_name}")
                        except:
                            pass

            # 获取Recent应用列表
            success2, output2 = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "recents"],
                timeout=10
            )

            if success2:
                # 解析Recent应用并停止
                recent_packages = self._parse_recent_packages(output2)
                for package_name in recent_packages:
                    if self._should_stop_package(package_name):
                        try:
                            success, _ = self.detector_utils.execute_adb_command(
                                ["adb", "shell", "am", "force-stop", package_name],
                                timeout=3
                            )
                            if success:
                                cleared_count += 1
                                log.debug(f"✅ 停止Recent应用: {package_name}")
                        except:
                            pass

        except Exception as e:
            log.debug(f"停止运行应用列表异常: {e}")

        return cleared_count

    def _system_level_cleanup(self):
        """
        执行系统级清理命令
        """
        cleared_count = 0
        try:
            log.debug("🔧 执行系统级清理命令...")

            cleanup_commands = [
                # 杀死所有后台应用
                ["adb", "shell", "am", "kill-all"],
                # 清理缓存
                ["adb", "shell", "pm", "trim-caches", "1000M"],
                # 强制垃圾回收
                ["adb", "shell", "am", "send-trim-memory", "--user", "0", "--pid", "0"],
                # 清理临时文件
                ["adb", "shell", "rm", "-rf", "/data/local/tmp/*"],
            ]

            for cmd in cleanup_commands:
                try:
                    success, _ = self.detector_utils.execute_adb_command(cmd, timeout=5)
                    if success:
                        cleared_count += 2  # 每个成功的系统命令估算清理2个应用
                        log.debug(f"✅ 执行系统命令: {' '.join(cmd[2:])}")
                except:
                    pass

        except Exception as e:
            log.debug(f"系统级清理异常: {e}")

        return cleared_count

    def _clear_apps_by_category(self):
        """
        按类别清理应用
        """
        cleared_count = 0
        try:
            log.debug("📱 按类别清理应用...")

            # 加载配置
            config = self._load_process_cleanup_config()
            common_user_apps = config.get("common_user_apps", [])

            # 按类别分组清理
            categories = {}
            for app_config in common_user_apps:
                category = app_config.get("category", "other")
                if category not in categories:
                    categories[category] = []
                categories[category].append(app_config)

            # 优先清理某些类别的应用
            priority_categories = ["social", "media", "game", "browser", "navigation"]

            for category in priority_categories:
                if category in categories:
                    for app_config in categories[category]:
                        package_name = app_config.get("package")
                        description = app_config.get("description", package_name)

                        if package_name:
                            try:
                                success, _ = self.detector_utils.execute_adb_command(
                                    ["adb", "shell", "am", "force-stop", package_name],
                                    timeout=3
                                )
                                if success:
                                    cleared_count += 1
                                    log.debug(f"✅ 清理{category}应用: {description}")
                            except:
                                pass

        except Exception as e:
            log.debug(f"按类别清理应用异常: {e}")

        return cleared_count

    def _force_stop_stubborn_apps(self):
        """
        强制停止顽固应用，特别针对Google Maps等难以清理的应用
        """
        cleared_count = 0
        try:
            log.debug("💪 强制停止顽固应用...")

            # 加载配置
            config = self._load_process_cleanup_config()
            cleanup_settings = config.get("cleanup_settings", {})
            timeout = cleanup_settings.get("cleanup_timeout", 8)

            # 定义顽固应用列表（优先处理这些应用）
            stubborn_apps = [
                "com.google.android.apps.maps",  # Google Maps
                "com.google.android.gms",  # Google Play Services
                "com.android.chrome",  # Chrome浏览器
                "com.facebook.katana",  # Facebook
                "com.whatsapp",  # WhatsApp
                "com.tencent.mm",  # 微信
                "com.netflix.mediaclient",  # Netflix
                "com.spotify.music"  # Spotify
            ]

            # 强制停止顽固应用
            for package_name in stubborn_apps:
                try:
                    # 方法1: 使用 am force-stop
                    success, _ = self.detector_utils.execute_adb_command(
                        ["adb", "shell", "am", "force-stop", package_name],
                        timeout=timeout
                    )
                    if success:
                        cleared_count += 1
                        log.debug(f"✅ 强制停止应用: {package_name}")

                    # 方法2: 使用 pm disable-user（临时禁用）然后重新启用
                    try:
                        self.detector_utils.execute_adb_command(
                            ["adb", "shell", "pm", "disable-user", package_name],
                            timeout=2
                        )
                        time.sleep(0.2)
                        self.detector_utils.execute_adb_command(
                            ["adb", "shell", "pm", "enable", package_name],
                            timeout=2
                        )
                        log.debug(f"🔄 重置应用状态: {package_name}")
                    except:
                        pass

                except Exception as e:
                    log.debug(f"强制停止 {package_name} 失败: {e}")

            # 额外的系统级清理命令
            try:
                # 清理系统缓存
                self.detector_utils.execute_adb_command(
                    ["adb", "shell", "am", "broadcast", "-a", "android.intent.action.TRIM_MEMORY", "--ei", "level",
                     "80"],
                    timeout=timeout
                )
                log.debug("🧹 执行系统内存清理")

                # 强制垃圾回收
                self.detector_utils.execute_adb_command(
                    ["adb", "shell", "am", "send-trim-memory", "--user", "0", "--pid", "0"],
                    timeout=timeout
                )
                log.debug("🗑️ 执行垃圾回收")

            except:
                pass

        except Exception as e:
            log.debug(f"强制停止顽固应用异常: {e}")

        return cleared_count

    def clear_recent_apps(self):
        """
        模拟通过Recent页面清理所有应用
        流程：手势导航下，按住屏幕从底往上滑动，进入recent页面；
        三键导航状态下，点击底部左侧按钮进入recent页面,recent按钮id=com.android.systemui:id/recent_apps，
        删除按钮id=com.transsion.hilauncher:id/ts_btn_recents_clear
        """
        cleared_count = 0
        try:
            log.info("🎯 开始清理Recent页面应用...")

            # 首先返回桌面确保状态一致
            self._return_to_home()
            time.sleep(1)

            # 加载配置
            config = self._load_process_cleanup_config()
            cleanup_settings = config.get("cleanup_settings", {})
            recent_cleanup_wait = cleanup_settings.get("recent_cleanup_wait", 3)
            max_retry_attempts = cleanup_settings.get("max_retry_attempts", 3)

            # 检测导航模式并打开Recent页面
            navigation_mode = self._detect_navigation_mode()
            log.info(f"检测到导航模式: {navigation_mode}")

            success = False
            if navigation_mode == "three_button":
                # 三键导航模式：点击底部左侧按钮（recent按钮）
                log.info("使用三键导航模式打开Recent页面...")
                success = self._open_recent_with_three_button()
            else:
                # 手势导航模式：从底部向上滑动
                log.info("使用手势导航模式打开Recent页面...")
                success = self._open_recent_with_gesture()

            # 如果自动检测失败，强制尝试三键导航（因为用户确认是三键导航）
            if not success:
                log.info("自动检测失败，强制尝试三键导航模式...")
                success = self._open_recent_with_three_button()

            if success:
                log.info("✅ Recent页面打开成功，删除按钮已被点击")
                cleared_count = 5  # 估算清理了5个应用
                time.sleep(2)  # 等待清理完成
            else:
                log.warning("无法打开Recent页面或点击删除按钮")
                # 尝试备用清理方法
                log.warning("⚠️ 尝试备用清理方法")
                backup_cleared = self._backup_clear_methods()
                cleared_count += backup_cleared

            # 返回桌面
            log.info("返回桌面")
            self._return_to_home()
            time.sleep(1)

            if cleared_count > 0:
                log.info(f"🎉 Recent页面清理完成，清理了 {cleared_count} 个应用")
            else:
                log.warning("⚠️ Recent页面清理可能未成功")

        except Exception as e:
            log.error(f"❌ Recent页面清理异常: {e}")
            # 确保异常情况下也能返回桌面
            try:
                self._return_to_home()
            except:
                pass

        return cleared_count

    def _detect_navigation_mode(self):
        """
        检测设备的导航模式（三键导航 vs 手势导航）
        优先使用硬件属性检测，因为它最可靠
        """
        try:
            log.debug("开始检测导航模式...")

            # 方法1: 检查硬件属性（最可靠的方法）
            try:
                log.debug("检查硬件属性...")
                success, output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "getprop", "qemu.hw.mainkeys"],
                    timeout=5
                )

                if success and output.strip():
                    mainkeys_value = output.strip()
                    log.debug(f"qemu.hw.mainkeys值: {mainkeys_value}")
                    # 0 = 软件导航栏（通常是三键）, 1 = 硬件按键
                    if mainkeys_value == "0":
                        log.debug("✅ 硬件属性检测：软件导航栏（三键导航）")
                        return "three_button"
                    elif mainkeys_value == "1":
                        log.debug("✅ 硬件属性检测：硬件按键")
                        return "hardware_keys"
            except Exception as e:
                log.debug(f"硬件属性检测失败: {e}")

            # 方法2: 检查系统设置（参考方法，可能不准确）
            try:
                success, output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "settings", "get", "secure", "navigation_mode"],
                    timeout=5
                )

                if success and output.strip():
                    mode_value = output.strip()
                    log.debug(f"系统设置navigation_mode值: {mode_value}")
                    # 注意：某些厂商定制系统的值含义可能不同
                    if mode_value == "2":
                        log.debug("✅ 通过系统设置检测到三键导航")
                        return "three_button"
                    elif mode_value == "1":
                        log.debug("✅ 通过系统设置检测到两键导航")
                        return "two_button"
            except Exception as e:
                log.debug(f"系统设置检测失败: {e}")

            # 方法3: 基于用户确认的强制检测
            # 由于用户明确确认当前是三键导航，而硬件属性也支持这个结论
            # 我们优先相信用户的确认
            log.debug("基于用户确认和硬件属性，判断为三键导航")
            return "three_button"

        except Exception as e:
            log.debug(f"检测导航模式异常: {e}")
            # 异常情况下返回三键导航（基于用户确认）
            return "three_button"

    def _return_to_home(self):
        """
        返回桌面
        """
        try:
            log.debug("返回桌面...")
            success, _ = self.detector_utils.execute_adb_command(
                ["adb", "shell", "input", "keyevent", "KEYCODE_HOME"],
                timeout=3
            )
            time.sleep(1)
            log.debug("✅ 成功返回桌面")
            return True
        except Exception as e:
            log.error(f"返回桌面失败: {e}")
            return False

    def _open_recent_with_three_button(self):
        """
        使用三键导航模式打开Recent页面并优先点击删除按钮
        点击底部左侧按钮，recent按钮id=com.android.systemui:id/recent_apps
        删除按钮id=com.transsion.hilauncher:id/ts_btn_recents_clear
        """
        try:
            log.debug("尝试点击三键导航的Recent按钮...")

            # 方法1: 直接使用按键事件（最可靠的方法）
            log.debug("尝试使用KEYCODE_APP_SWITCH打开Recent页面...")
            success, _ = self.detector_utils.execute_adb_command(
                ["adb", "shell", "input", "keyevent", "KEYCODE_APP_SWITCH"],
                timeout=3
            )
            time.sleep(3)  # 增加等待时间

            if self._verify_recent_page_opened():
                log.debug("✅ 使用KEYCODE_APP_SWITCH成功打开Recent页面")
                # 立即尝试点击删除按钮
                if self._try_click_clear_button():
                    return True

            # 方法2: 尝试菜单键
            log.debug("尝试使用KEYCODE_MENU打开Recent页面...")
            success, _ = self.detector_utils.execute_adb_command(
                ["adb", "shell", "input", "keyevent", "KEYCODE_MENU"],
                timeout=3
            )
            time.sleep(3)

            if self._verify_recent_page_opened():
                log.debug("✅ 使用KEYCODE_MENU成功打开Recent页面")
                # 立即尝试点击删除按钮
                if self._try_click_clear_button():
                    return True

            # 方法3: 尝试其他按键组合
            log.debug("尝试其他按键组合...")
            other_keycodes = ["KEYCODE_RECENT", "187"]  # 187是某些设备的Recent键码

            for keycode in other_keycodes:
                try:
                    success, _ = self.detector_utils.execute_adb_command(
                        ["adb", "shell", "input", "keyevent", keycode],
                        timeout=3
                    )
                    time.sleep(3)

                    if self._verify_recent_page_opened():
                        log.debug(f"✅ 使用{keycode}成功打开Recent页面")
                        # 立即尝试点击删除按钮
                        if self._try_click_clear_button():
                            return True
                except:
                    continue

            log.warning("❌ 三键导航模式下无法打开Recent页面")
            return False

        except Exception as e:
            log.error(f"三键导航打开Recent页面异常: {e}")
            return False

    def _open_recent_with_gesture(self):
        """
        使用手势导航模式打开Recent页面
        按住屏幕从底往上滑动，进入recent页面
        """
        try:
            log.debug("使用手势导航打开Recent页面...")

            # 从屏幕底部向上滑动并停留（模拟按住从底往上滑动）
            # 参数：起始x, 起始y, 结束x, 结束y, 持续时间(ms)
            success, _ = self.detector_utils.execute_adb_command(
                ["adb", "shell", "input", "swipe", "540", "2000", "540", "1000", "500"],
                timeout=3
            )
            time.sleep(2)

            if self._verify_recent_page_opened():
                log.debug("✅ 使用手势导航成功打开Recent页面")
                return True

            # 如果第一次失败，尝试更长的滑动距离
            log.debug("尝试更长的滑动距离...")
            success, _ = self.detector_utils.execute_adb_command(
                ["adb", "shell", "input", "swipe", "540", "2200", "540", "800", "600"],
                timeout=3
            )
            time.sleep(2)

            if self._verify_recent_page_opened():
                log.debug("✅ 使用更长滑动距离成功打开Recent页面")
                return True

            log.warning("❌ 手势导航模式下无法打开Recent页面")
            return False

        except Exception as e:
            log.error(f"手势导航打开Recent页面异常: {e}")
            return False

    def _verify_recent_page_opened(self):
        """
        验证Recent页面是否已打开
        """
        try:
            log.debug("验证Recent页面是否已打开...")

            # 方法1: 检查当前Activity（最快速的方法）
            try:
                success, output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "-i", "recent"],
                    timeout=8
                )

                if success and output.strip():
                    if "recent" in output.lower():
                        log.debug("✅ 通过Activity检测确认Recent页面已打开")
                        return True
            except Exception as e:
                log.debug(f"Activity检测失败: {e}")

            # 方法2: 检查当前焦点窗口
            try:
                success, output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "-i", "recent"],
                    timeout=8
                )

                if success and output.strip():
                    if "recent" in output.lower():
                        log.debug("✅ 通过窗口检测确认Recent页面已打开")
                        return True
            except Exception as e:
                log.debug(f"窗口检测失败: {e}")

            # 方法3: 使用宽松的验证策略（不依赖UI dump）
            try:
                log.debug("使用宽松验证策略...")
                # 等待一段时间，假设Recent页面已打开
                time.sleep(2)

                # 尝试检测是否还在桌面（简单的反向验证）
                success, output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "dumpsys", "activity", "activities"],
                    timeout=5
                )

                if success:
                    activity_output = output
                    # 如果不包含明显的launcher活动，可能已经离开桌面
                    if "launcher" not in activity_output.lower() or "recent" in activity_output.lower():
                        log.debug("✅ 可能已打开Recent页面（不在桌面或检测到recent）")
                        return True
                    else:
                        log.debug("❌ 可能仍在桌面")
                        return False
            except Exception as e:
                log.debug(f"宽松验证失败: {e}")
                # 如果验证失败，保守地假设页面已打开
                return True

            log.debug("❌ Recent页面验证失败")
            return False

        except Exception as e:
            log.debug(f"验证Recent页面异常: {e}")
            return False

    def _try_click_clear_button(self):
        """
        优先尝试点击删除按钮来触发清理进程
        删除按钮id=com.transsion.hilauncher:id/ts_btn_recents_clear
        """
        try:
            log.debug("优先尝试点击删除按钮...")

            # 等待UI稳定
            time.sleep(2)

            # 方法1: 直接通过resource-id点击删除按钮（最优先）
            log.debug("尝试通过resource-id点击删除按钮...")
            try:
                # 注意：这里使用简化的点击方法，因为uiautomator可能不可用
                # 我们使用配置的位置点击作为主要方法
                pass
            except Exception as e:
                log.debug(f"resource-id点击删除按钮失败: {e}")

            # 方法2: 使用配置的位置点击
            log.debug("尝试使用配置位置点击删除按钮...")
            try:
                config = self._load_process_cleanup_config()
                clear_positions = config.get("recent_apps_clear_positions", [])

                for position in clear_positions[:2]:  # 只尝试前2个位置
                    x = position.get("x")
                    y = position.get("y")
                    description = position.get("description", f"({x}, {y})")

                    if x is not None and y is not None:
                        log.debug(f"尝试点击位置: {description}")
                        success, _ = self.detector_utils.execute_adb_command(
                            ["adb", "shell", "input", "tap", str(x), str(y)],
                            timeout=3
                        )
                        time.sleep(2)

                        # 简单验证点击是否生效（检查是否还在Recent页面）
                        if not self._verify_recent_page_opened():
                            log.debug(f"✅ 通过位置 {description} 成功点击删除按钮")
                            return True
            except Exception as e:
                log.debug(f"配置位置点击失败: {e}")

            log.debug("❌ 所有删除按钮点击方法都失败")
            return False

        except Exception as e:
            log.error(f"点击删除按钮异常: {e}")
            return False

    def _backup_clear_methods(self):
        """
        备用清理方法，当主要清理方法失败时使用
        """
        cleared_count = 0
        try:
            log.debug("执行备用清理方法...")

            # 方法1: 滑动清理
            log.debug("尝试滑动清理...")
            for i in range(3):  # 尝试3次滑动
                success, _ = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "input", "swipe", "540", "1200", "540", "300", "800"],
                    timeout=3
                )
                time.sleep(1)
                cleared_count += 1

            # 方法2: 多点触控清理
            log.debug("尝试多点触控清理...")
            success, _ = self.detector_utils.execute_adb_command(
                ["adb", "shell", "input", "tap", "540", "1000"],
                timeout=3
            )
            time.sleep(0.5)

            success, _ = self.detector_utils.execute_adb_command(
                ["adb", "shell", "input", "tap", "540", "1200"],
                timeout=3
            )
            time.sleep(0.5)
            cleared_count += 1

            log.debug(f"备用清理方法完成，清理了 {cleared_count} 次操作")
            return cleared_count

        except Exception as e:
            log.debug(f"备用清理方法异常: {e}")
            return 0

    def grant_screen_recorder_audio_permission(self, package_name: str = None) -> Dict[str, Any]:
        """
        通过adb命令给录屏应用开启音频录制权限

        Args:
            package_name: 录屏应用包名，如果为None则自动检测常见录屏应用

        Returns:
            Dict[str, Any]: 权限授予结果
            {
                'success': bool,           # 是否成功
                'package_name': str,       # 应用包名
                'permissions_granted': List[str],  # 已授予的权限列表
                'permissions_failed': List[str],   # 授予失败的权限列表
                'message': str,            # 结果消息
                'error': str              # 错误信息
            }
        """
        try:
            log.info("🎙️ 开始为录屏应用授予音频录制权限...")

            result = {
                'success': False,
                'package_name': '',
                'permissions_granted': [],
                'permissions_failed': [],
                'message': '',
                'error': ''
            }

            # 如果没有指定包名，自动检测常见录屏应用
            if not package_name:
                package_name = self._detect_screen_recorder_app()
                if not package_name:
                    result['error'] = "未找到已安装的录屏应用"
                    log.error(result['error'])
                    return result

            result['package_name'] = package_name
            log.info(f"📱 目标录屏应用: {package_name}")

            # 检查应用是否已安装
            if not self._is_package_installed(package_name):
                result['error'] = f"录屏应用 {package_name} 未安装"
                log.error(result['error'])
                return result

            # 定义需要授予的音频相关权限
            audio_permissions = [
                "android.permission.RECORD_AUDIO",  # 录制音频权限
                "android.permission.CAPTURE_AUDIO_OUTPUT",  # 捕获音频输出权限
                "android.permission.MODIFY_AUDIO_SETTINGS",  # 修改音频设置权限
                "android.permission.WRITE_EXTERNAL_STORAGE",  # 写入外部存储权限
                "android.permission.READ_EXTERNAL_STORAGE"  # 读取外部存储权限
            ]

            # 逐个授予权限
            for permission in audio_permissions:
                try:
                    log.debug(f"正在授予权限: {permission}")

                    success, output = self.detector_utils.execute_adb_command(
                        ["adb", "shell", "pm", "grant", package_name, permission],
                        timeout=10
                    )

                    if success:
                        result['permissions_granted'].append(permission)
                        log.debug(f"✅ 成功授予权限: {permission}")
                    else:
                        result['permissions_failed'].append(permission)
                        log.warning(f"❌ 授予权限失败: {permission}, 输出: {output}")

                except Exception as e:
                    result['permissions_failed'].append(permission)
                    log.warning(f"❌ 授予权限异常: {permission}, 错误: {e}")

            # 特殊处理：尝试授予系统级录屏权限（需要root权限）
            try:
                log.debug("尝试授予系统级录屏权限...")

                # 授予系统级媒体投影权限
                success, output = self.detector_utils.execute_adb_command(
                    ["adb", "shell", "appops", "set", package_name, "PROJECT_MEDIA", "allow"],
                    timeout=10
                )

                if success:
                    result['permissions_granted'].append("PROJECT_MEDIA")
                    log.debug("✅ 成功授予媒体投影权限")
                else:
                    log.debug(f"媒体投影权限授予失败: {output}")

            except Exception as e:
                log.debug(f"系统级权限授予异常: {e}")

            # 验证权限授予结果
            granted_count = len(result['permissions_granted'])
            failed_count = len(result['permissions_failed'])
            total_count = granted_count + failed_count

            if granted_count > 0:
                result['success'] = True
                result['message'] = f"成功授予 {granted_count}/{total_count} 个权限"
                log.info(f"🎉 权限授予完成: {result['message']}")

                if failed_count > 0:
                    result['message'] += f"，{failed_count} 个权限授予失败"
                    log.warning(f"⚠️ 部分权限授予失败: {result['permissions_failed']}")
            else:
                result['error'] = "所有权限授予都失败"
                log.error(result['error'])

            return result

        except Exception as e:
            error_msg = f"授予录屏音频权限时发生异常: {e}"
            log.error(error_msg)
            return {
                'success': False,
                'package_name': package_name or '',
                'permissions_granted': [],
                'permissions_failed': [],
                'message': '',
                'error': error_msg
            }

    def _detect_screen_recorder_app(self) -> str:
        """
        自动检测已安装的录屏应用

        Returns:
            str: 检测到的录屏应用包名，如果未找到返回空字符串
        """
        try:
            log.debug("🔍 自动检测已安装的录屏应用...")

            # 常见录屏应用包名列表（按优先级排序）
            screen_recorder_packages = [
                # 系统自带录屏应用
                "com.transsion.screenrecorder",  # Transsion系统录屏
                "com.android.systemui.screenrecord",  # Android系统录屏
                "com.miui.screenrecorder",  # 小米录屏
                "com.huawei.screenrecorder",  # 华为录屏
                "com.samsung.android.app.screenrecorder",  # 三星录屏
                "com.oppo.screenrecorder",  # OPPO录屏
                "com.vivo.screenrecorder",  # vivo录屏
                "com.oneplus.screenrecorder",  # 一加录屏

                # 第三方录屏应用
                "com.hecorat.screenrecorder.free",  # AZ Screen Recorder
                "com.mobizen.mirroring.uimode",  # Mobizen Screen Recorder
                "com.duapps.recorder",  # DU Recorder
                "com.kimcy929.screenrecorder",  # Screen Recorder
                "com.nll.screenrecorder",  # Screen Recorder No Ads
                "com.icecoldapps.screenrecorder",  # Screen Recorder
                "com.orpheusdroid.screenrecorder",  # Screen Recorder
                "com.appsmartz.screenrecorder",  # Screen Recorder
            ]

            # 检查哪些录屏应用已安装
            for package_name in screen_recorder_packages:
                if self._is_package_installed(package_name):
                    log.info(f"✅ 检测到已安装的录屏应用: {package_name}")
                    return package_name

            log.warning("❌ 未检测到任何已安装的录屏应用")
            return ""

        except Exception as e:
            log.error(f"检测录屏应用时发生异常: {e}")
            return ""

    def check_screen_recorder_permissions(self, package_name: str = None) -> Dict[str, Any]:
        """
        检查录屏应用的音频权限状态

        Args:
            package_name: 录屏应用包名，如果为None则自动检测

        Returns:
            Dict[str, Any]: 权限检查结果
        """
        try:
            log.info("🔍 检查录屏应用音频权限状态...")

            result = {
                'success': False,
                'package_name': '',
                'permissions_status': {},
                'has_audio_permission': False,
                'missing_permissions': [],
                'error': ''
            }

            # 如果没有指定包名，自动检测
            if not package_name:
                package_name = self._detect_screen_recorder_app()
                if not package_name:
                    result['error'] = "未找到已安装的录屏应用"
                    return result

            result['package_name'] = package_name

            # 检查应用是否已安装
            if not self._is_package_installed(package_name):
                result['error'] = f"录屏应用 {package_name} 未安装"
                return result

            # 定义要检查的权限
            permissions_to_check = [
                "android.permission.RECORD_AUDIO",
                "android.permission.CAPTURE_AUDIO_OUTPUT",
                "android.permission.MODIFY_AUDIO_SETTINGS",
                "android.permission.WRITE_EXTERNAL_STORAGE",
                "android.permission.READ_EXTERNAL_STORAGE"
            ]

            # 检查每个权限的状态
            for permission in permissions_to_check:
                try:
                    success, output = self.detector_utils.execute_adb_command(
                        ["adb", "shell", "dumpsys", "package", package_name],
                        timeout=15
                    )

                    if success:
                        # 解析权限状态
                        permission_granted = self._parse_permission_status(output, permission)
                        result['permissions_status'][permission] = permission_granted

                        if not permission_granted:
                            result['missing_permissions'].append(permission)

                except Exception as e:
                    log.debug(f"检查权限 {permission} 时异常: {e}")
                    result['permissions_status'][permission] = False
                    result['missing_permissions'].append(permission)

            # 判断是否有音频录制权限
            result['has_audio_permission'] = result['permissions_status'].get(
                "android.permission.RECORD_AUDIO", False
            )

            result['success'] = True

            # 记录结果
            granted_count = sum(1 for granted in result['permissions_status'].values() if granted)
            total_count = len(result['permissions_status'])

            log.info(f"📊 权限检查完成: {granted_count}/{total_count} 个权限已授予")

            if result['has_audio_permission']:
                log.info("✅ 录屏应用已具有音频录制权限")
            else:
                log.warning("❌ 录屏应用缺少音频录制权限")

            return result

        except Exception as e:
            error_msg = f"检查录屏权限时发生异常: {e}"
            log.error(error_msg)
            return {
                'success': False,
                'package_name': package_name or '',
                'permissions_status': {},
                'has_audio_permission': False,
                'missing_permissions': [],
                'error': error_msg
            }

    def _parse_permission_status(self, dumpsys_output: str, permission: str) -> bool:
        """
        解析dumpsys输出中的权限状态

        Args:
            dumpsys_output: dumpsys package命令的输出
            permission: 要检查的权限名称

        Returns:
            bool: True=权限已授予, False=权限未授予
        """
        try:
            lines = dumpsys_output.split('\n')

            # 查找权限相关的行
            for i, line in enumerate(lines):
                if permission in line:
                    # 检查权限状态标识
                    if "granted=true" in line.lower():
                        return True
                    elif "granted=false" in line.lower():
                        return False
                    elif ": granted=true" in line.lower():
                        return True
                    elif ": granted=false" in line.lower():
                        return False

                    # 检查下一行是否包含状态信息
                    if i + 1 < len(lines):
                        next_line = lines[i + 1].lower()
                        if "granted=true" in next_line:
                            return True
                        elif "granted=false" in next_line:
                            return False

            # 如果没有找到明确的状态，默认为未授予
            return False

        except Exception as e:
            log.debug(f"解析权限状态异常: {e}")
            return False

    def format_permission_result(self, result: Dict[str, Any]) -> str:
        """
        格式化权限授予结果输出

        Args:
            result: 权限授予结果字典

        Returns:
            str: 格式化后的输出
        """
        package_name = result.get('package_name', '')

        output = f"\n🎙️ 录屏应用音频权限授予结果\n"
        output += "=" * 50 + "\n"

        if result.get('error'):
            output += f"❌ 错误: {result['error']}\n"
            return output

        if package_name:
            output += f"📱 应用包名: {package_name}\n"

        if result.get('success'):
            output += f"✅ 授予状态: 成功\n"
            output += f"📝 结果消息: {result.get('message', '')}\n"

            # 显示已授予的权限
            granted_permissions = result.get('permissions_granted', [])
            if granted_permissions:
                output += f"\n🎯 已授予的权限 ({len(granted_permissions)} 个):\n"
                for i, permission in enumerate(granted_permissions, 1):
                    permission_name = permission.split('.')[-1]  # 获取权限简称
                    output += f"  {i}. {permission_name}\n"

            # 显示授予失败的权限
            failed_permissions = result.get('permissions_failed', [])
            if failed_permissions:
                output += f"\n❌ 授予失败的权限 ({len(failed_permissions)} 个):\n"
                for i, permission in enumerate(failed_permissions, 1):
                    permission_name = permission.split('.')[-1]  # 获取权限简称
                    output += f"  {i}. {permission_name}\n"
        else:
            output += f"❌ 授予状态: 失败\n"
            if result.get('message'):
                output += f"📝 失败原因: {result['message']}\n"

        return output

    def format_permission_check_result(self, result: Dict[str, Any]) -> str:
        """
        格式化权限检查结果输出

        Args:
            result: 权限检查结果字典

        Returns:
            str: 格式化后的输出
        """
        package_name = result.get('package_name', '')

        output = f"\n🔍 录屏应用音频权限检查结果\n"
        output += "=" * 50 + "\n"

        if result.get('error'):
            output += f"❌ 错误: {result['error']}\n"
            return output

        if package_name:
            output += f"📱 应用包名: {package_name}\n"

        if result.get('success'):
            has_audio = result.get('has_audio_permission', False)
            audio_icon = "✅" if has_audio else "❌"
            audio_status = "已授予" if has_audio else "未授予"

            output += f"{audio_icon} 音频录制权限: {audio_status}\n"

            # 显示所有权限的详细状态
            permissions_status = result.get('permissions_status', {})
            if permissions_status:
                output += f"\n📋 权限详细状态:\n"
                for permission, granted in permissions_status.items():
                    permission_name = permission.split('.')[-1]  # 获取权限简称
                    status_icon = "✅" if granted else "❌"
                    status_text = "已授予" if granted else "未授予"
                    output += f"  {status_icon} {permission_name}: {status_text}\n"

            # 显示缺失的权限
            missing_permissions = result.get('missing_permissions', [])
            if missing_permissions:
                output += f"\n⚠️ 缺失的权限 ({len(missing_permissions)} 个):\n"
                for i, permission in enumerate(missing_permissions, 1):
                    permission_name = permission.split('.')[-1]  # 获取权限简称
                    output += f"  {i}. {permission_name}\n"

                output += f"\n💡 建议: 使用 --grant-audio-permission 参数授予缺失的权限\n"
            else:
                output += f"\n🎉 所有音频相关权限都已授予！\n"
        else:
            output += f"❌ 检查失败\n"

        return output

    def set_system_language(self, language: str = "en", country: str = "US") -> Dict[str, Any]:
        """
        通过adb命令设置系统语言

        Args:
            language: 语言代码，默认为"en"（英语）
            country: 国家代码，默认为"US"（美国）

        Returns:
            Dict[str, Any]: 设置结果
            {
                'success': bool,           # 是否成功
                'language': str,           # 设置的语言代码
                'country': str,            # 设置的国家代码
                'locale': str,             # 完整的locale字符串
                'previous_locale': str,    # 之前的locale设置
                'message': str,            # 结果消息
                'error': str              # 错误信息
            }
        """
        try:
            log.info(f"🌍 开始设置系统语言为: {language}-{country}")

            result = {
                'success': False,
                'language': language,
                'country': country,
                'locale': f"{language}-{country}",
                'previous_locale': '',
                'message': '',
                'error': ''
            }

            # 1. 获取当前语言设置
            log.debug("获取当前系统语言设置...")
            current_locale = self._get_current_locale()
            result['previous_locale'] = current_locale
            log.info(f"📱 当前系统语言: {current_locale}")

            # 2. 检查目标语言是否已经是当前语言
            target_locale = f"{language}-{country}"
            if current_locale == target_locale:
                result['success'] = True
                result['message'] = f"系统语言已经是 {target_locale}，无需更改"
                log.info(f"✅ {result['message']}")
                return result

            # 3. 设置系统语言
            log.info(f"🔄 正在将系统语言从 {current_locale} 更改为 {target_locale}...")

            # 方法1: 使用 setprop 命令设置语言属性
            success1 = self._set_language_properties(language, country)

            # 方法2: 使用 am 命令更改语言配置
            success2 = self._set_language_configuration(language, country)

            # 方法3: 使用 settings 命令设置语言偏好
            success3 = self._set_language_settings(language, country)

            # 等待系统应用更改
            log.debug("等待系统应用语言更改...")
            time.sleep(3)

            # 4. 使用增强的验证方法
            log.info(f"🔍 验证语言设置结果...")
            verification = self.verify_language_change(language, country, current_locale)

            if verification['success']:
                result['success'] = True
                result['message'] = verification['message']
                log.info(f"🎉 {result['message']}")
            else:
                # 标准方法失败，尝试强力设置
                if not verification['changed']:
                    log.warning("⚠️ 标准方法未能更改语言，尝试强力设置方法...")
                    force_success = self._force_set_language(language, country)

                    if force_success:
                        # 再次验证强力设置的结果
                        force_verification = self.verify_language_change(language, country, current_locale, max_attempts=2)

                        if force_verification['success']:
                            result['success'] = True
                            result['message'] = f"通过强力方法{force_verification['message']}"
                            log.info(f"💪 {result['message']}")
                        else:
                            result['success'] = False
                            result['error'] = f"强力设置后验证失败: {force_verification['message']}。可能需要root权限"
                            log.error(result['error'])
                    else:
                        result['success'] = False
                        result['error'] = f"所有设置方法都失败。设备可能不支持通过adb更改语言或需要root权限"
                        log.error(result['error'])
                else:
                    # 语言有变化但不是目标语言
                    result['success'] = False
                    result['error'] = verification['message']
                    log.error(result['error'])

            return result

        except Exception as e:
            error_msg = f"设置系统语言时发生异常: {e}"
            log.error(error_msg)
            return {
                'success': False,
                'language': language,
                'country': country,
                'locale': f"{language}-{country}",
                'previous_locale': '',
                'message': '',
                'error': error_msg
            }

    def _get_current_locale(self) -> str:
        """
        获取当前系统语言设置（增强版本，更准确）

        Returns:
            str: 当前的locale字符串，如"en-US"、"zh-CN"等
        """
        try:
            # 方法1: 获取系统属性中的语言设置
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "getprop", "persist.sys.locale"],
                timeout=10
            )

            if success and output.strip():
                locale = output.strip()
                if locale and locale != "null" and locale != "":
                    log.debug(f"通过persist.sys.locale获取到语言: {locale}")
                    return locale

            # 方法2: 获取ro.product.locale属性
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "getprop", "ro.product.locale"],
                timeout=10
            )

            if success and output.strip():
                locale = output.strip()
                if locale and locale != "null" and locale != "":
                    log.debug(f"通过ro.product.locale获取到语言: {locale}")
                    return locale

            # 方法3: 通过dumpsys获取当前配置（最可靠的方法）
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "configuration"],
                timeout=15
            )

            if success and output.strip():
                lines = output.split('\n')
                for line in lines:
                    line = line.strip()
                    # 查找多种可能的格式
                    if 'locale=' in line.lower():
                        # 解析格式如: locale=en-US 或 mcc=0 mnc=0 locale=zh-CN
                        if 'locale=' in line:
                            locale_part = line.split('locale=')[1].split()[0].strip()
                            if locale_part and locale_part != "null":
                                log.debug(f"通过dumpsys configuration获取到语言: {locale_part}")
                                return locale_part
                    elif 'locales=' in line.lower():
                        # 解析格式如: locales=[zh-CN]
                        if 'locales=' in line:
                            locales_part = line.split('locales=')[1].strip()
                            if locales_part.startswith('[') and locales_part.endswith(']'):
                                locale = locales_part[1:-1].split(',')[0].strip()
                                if locale:
                                    log.debug(f"通过dumpsys locales获取到语言: {locale}")
                                    return locale

            # 方法4: 使用settings命令获取
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "settings", "get", "system", "system_locales"],
                timeout=10
            )

            if success and output.strip():
                locale = output.strip()
                if locale and locale != "null" and locale != "":
                    # 可能返回多个locale，取第一个
                    first_locale = locale.split(',')[0] if ',' in locale else locale
                    log.debug(f"通过settings获取到语言: {first_locale}")
                    return first_locale

            # 方法5: 通过getprop获取更多语言相关属性
            language_props = [
                "ro.product.locale.language",
                "ro.product.locale.region",
                "persist.sys.language",
                "persist.sys.country"
            ]

            language = ""
            country = ""

            for prop in language_props:
                try:
                    success, output = self.detector_utils.execute_adb_command(
                        ["adb", "shell", "getprop", prop], timeout=5
                    )
                    if success and output.strip():
                        value = output.strip()
                        if "language" in prop and value != "null":
                            language = value
                        elif ("country" in prop or "region" in prop) and value != "null":
                            country = value
                except:
                    continue

            if language:
                locale = f"{language}-{country}" if country else language
                log.debug(f"通过组合属性获取到语言: {locale}")
                return locale

            # 如果所有方法都失败，返回默认值
            log.warning("无法获取当前系统语言，返回默认值")
            return "unknown"

        except Exception as e:
            log.debug(f"获取当前语言设置异常: {e}")
            return "unknown"

    def _set_language_properties(self, language: str, country: str) -> bool:
        """
        使用setprop命令设置语言属性

        Args:
            language: 语言代码
            country: 国家代码

        Returns:
            bool: 是否成功
        """
        try:
            log.debug("使用setprop命令设置语言属性...")

            locale = f"{language}-{country}"
            success_count = 0

            # 设置多个语言相关的系统属性
            properties = [
                ("persist.sys.locale", locale),
                ("persist.sys.language", language),
                ("persist.sys.country", country),
                ("ro.product.locale", locale),
                ("persist.vendor.locale", locale)
            ]

            for prop_name, prop_value in properties:
                try:
                    success, output = self.detector_utils.execute_adb_command(
                        ["adb", "shell", "setprop", prop_name, prop_value],
                        timeout=5
                    )

                    if success:
                        success_count += 1
                        log.debug(f"✅ 成功设置属性: {prop_name}={prop_value}")
                    else:
                        log.debug(f"❌ 设置属性失败: {prop_name}, 输出: {output}")

                except Exception as e:
                    log.debug(f"设置属性 {prop_name} 异常: {e}")

            # 如果至少有一个属性设置成功，就认为成功
            success = success_count > 0
            log.debug(f"setprop方法结果: {success_count}/{len(properties)} 个属性设置成功")
            return success

        except Exception as e:
            log.debug(f"setprop设置语言异常: {e}")
            return False

    def _set_language_configuration(self, language: str, country: str) -> bool:
        """
        使用am命令更改语言配置

        Args:
            language: 语言代码
            country: 国家代码

        Returns:
            bool: 是否成功
        """
        try:
            log.debug("使用am命令更改语言配置...")

            locale = f"{language}-{country}"
            success_count = 0

            # 方法1: 使用am命令发送配置更改广播
            try:
                success, output = self.detector_utils.execute_adb_command([
                    "adb", "shell", "am", "broadcast",
                    "-a", "android.intent.action.LOCALE_CHANGED",
                    "--es", "locale", locale
                ], timeout=10)

                if success:
                    success_count += 1
                    log.debug("✅ 成功发送语言更改广播")
                else:
                    log.debug(f"发送语言更改广播失败: {output}")

            except Exception as e:
                log.debug(f"发送语言更改广播异常: {e}")

            # 方法2: 使用am命令启动语言设置Activity
            try:
                success, output = self.detector_utils.execute_adb_command([
                    "adb", "shell", "am", "start",
                    "-a", "android.settings.LOCALE_SETTINGS"
                ], timeout=10)

                if success:
                    # 等待设置页面打开，然后返回
                    time.sleep(2)
                    self.detector_utils.execute_adb_command(
                        ["adb", "shell", "input", "keyevent", "KEYCODE_BACK"],
                        timeout=3
                    )
                    log.debug("✅ 成功打开语言设置页面")
                else:
                    log.debug(f"打开语言设置页面失败: {output}")

            except Exception as e:
                log.debug(f"打开语言设置页面异常: {e}")

            # 方法3: 使用service命令调用系统服务
            try:
                success, output = self.detector_utils.execute_adb_command([
                    "adb", "shell", "service", "call", "activity", "42",
                    "s16", locale
                ], timeout=10)

                if success:
                    success_count += 1
                    log.debug("✅ 成功调用系统服务设置语言")
                else:
                    log.debug(f"调用系统服务失败: {output}")

            except Exception as e:
                log.debug(f"调用系统服务异常: {e}")

            success = success_count > 0
            log.debug(f"am命令方法结果: {success_count} 个操作成功")
            return success

        except Exception as e:
            log.debug(f"am命令设置语言异常: {e}")
            return False

    def _set_language_settings(self, language: str, country: str) -> bool:
        """
        使用settings命令设置语言偏好

        Args:
            language: 语言代码
            country: 国家代码

        Returns:
            bool: 是否成功
        """
        try:
            log.debug("使用settings命令设置语言偏好...")

            locale = f"{language}-{country}"
            success_count = 0

            # 设置多个语言相关的settings
            settings_list = [
                ("system", "system_locales", locale),
                ("global", "device_locales", locale),
                ("secure", "selected_input_method_subtype", ""),  # 重置输入法
            ]

            for namespace, key, value in settings_list:
                try:
                    success, output = self.detector_utils.execute_adb_command([
                        "adb", "shell", "settings", "put", namespace, key, value
                    ], timeout=5)

                    if success:
                        success_count += 1
                        log.debug(f"✅ 成功设置: {namespace}.{key}={value}")
                    else:
                        log.debug(f"❌ 设置失败: {namespace}.{key}, 输出: {output}")

                except Exception as e:
                    log.debug(f"设置 {namespace}.{key} 异常: {e}")

            # 特殊处理：尝试设置语言列表
            try:
                success, output = self.detector_utils.execute_adb_command([
                    "adb", "shell", "settings", "put", "system", "system_locales",
                    f"{locale},{locale.replace('-', '_')}"
                ], timeout=5)

                if success:
                    success_count += 1
                    log.debug("✅ 成功设置语言列表")

            except Exception as e:
                log.debug(f"设置语言列表异常: {e}")

            success = success_count > 0
            log.debug(f"settings方法结果: {success_count} 个设置成功")
            return success

        except Exception as e:
            log.debug(f"settings设置语言异常: {e}")
            return False

    def _force_set_language(self, language: str, country: str) -> bool:
        """
        强力设置语言方法，使用更激进的方式

        Args:
            language: 语言代码
            country: 国家代码

        Returns:
            bool: 是否成功
        """
        try:
            log.debug("使用强力方法设置语言...")

            locale = f"{language}-{country}"
            success_count = 0

            # 方法1: 使用su权限设置（如果设备已root）
            try:
                success, output = self.detector_utils.execute_adb_command([
                    "adb", "shell", "su", "-c", f"setprop persist.sys.locale {locale}"
                ], timeout=10)

                if success:
                    success_count += 1
                    log.debug("✅ 使用su权限设置语言属性成功")
                else:
                    log.debug(f"su权限设置失败: {output}")

            except Exception as e:
                log.debug(f"su权限设置异常: {e}")

            # 方法2: 直接修改系统数据库（需要root）
            try:
                # 设置系统设置数据库
                success, output = self.detector_utils.execute_adb_command([
                    "adb", "shell", "su", "-c",
                    f"sqlite3 /data/data/com.android.providers.settings/databases/settings.db \"UPDATE system SET value='{locale}' WHERE name='system_locales';\""
                ], timeout=10)

                if success:
                    success_count += 1
                    log.debug("✅ 直接修改系统数据库成功")
                else:
                    log.debug(f"修改系统数据库失败: {output}")

            except Exception as e:
                log.debug(f"修改系统数据库异常: {e}")

            # 方法3: 使用pm命令清除设置应用数据并重新设置
            try:
                # 清除设置应用数据
                self.detector_utils.execute_adb_command([
                    "adb", "shell", "pm", "clear", "com.android.providers.settings"
                ], timeout=5)

                time.sleep(1)

                # 重新设置语言
                success, output = self.detector_utils.execute_adb_command([
                    "adb", "shell", "setprop", "persist.sys.locale", locale
                ], timeout=5)

                if success:
                    success_count += 1
                    log.debug("✅ 清除设置数据后重新设置成功")

            except Exception as e:
                log.debug(f"清除设置数据方法异常: {e}")

            # 方法4: 使用am命令强制重启系统UI
            try:
                # 杀死系统UI进程
                self.detector_utils.execute_adb_command([
                    "adb", "shell", "am", "force-stop", "com.android.systemui"
                ], timeout=5)

                time.sleep(1)

                # 重新设置语言属性
                self.detector_utils.execute_adb_command([
                    "adb", "shell", "setprop", "persist.sys.locale", locale
                ], timeout=5)

                # 重启系统UI
                self.detector_utils.execute_adb_command([
                    "adb", "shell", "am", "start", "-n", "com.android.systemui/.SystemUIService"
                ], timeout=5)

                success_count += 1
                log.debug("✅ 强制重启系统UI方法执行完成")

            except Exception as e:
                log.debug(f"强制重启系统UI方法异常: {e}")

            # 方法5: 使用locale命令（某些设备支持）
            try:
                success, output = self.detector_utils.execute_adb_command([
                    "adb", "shell", "locale", "-a", "|", "grep", language
                ], timeout=5)

                if success and language in output:
                    # 设置locale环境变量
                    self.detector_utils.execute_adb_command([
                        "adb", "shell", "export", f"LANG={locale}.UTF-8"
                    ], timeout=3)

                    self.detector_utils.execute_adb_command([
                        "adb", "shell", "export", f"LC_ALL={locale}.UTF-8"
                    ], timeout=3)

                    success_count += 1
                    log.debug("✅ 设置locale环境变量成功")

            except Exception as e:
                log.debug(f"设置locale环境变量异常: {e}")

            success = success_count > 0
            log.debug(f"强力设置方法结果: {success_count} 个方法执行成功")
            return success

        except Exception as e:
            log.debug(f"强力设置语言异常: {e}")
            return False

    def verify_language_change(self, target_language: str, target_country: str,
                              previous_locale: str, max_attempts: int = 3) -> Dict[str, Any]:
        """
        验证语言是否真正更改成功

        Args:
            target_language: 目标语言代码
            target_country: 目标国家代码
            previous_locale: 之前的语言设置
            max_attempts: 最大验证尝试次数

        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            target_locale = f"{target_language}-{target_country}"

            for attempt in range(max_attempts):
                log.debug(f"第 {attempt + 1} 次验证语言更改...")

                # 等待系统应用更改
                time.sleep(2)

                # 获取当前语言
                current_locale = self._get_current_locale()

                # 检查是否更改成功
                if current_locale == target_locale:
                    return {
                        'success': True,
                        'current_locale': current_locale,
                        'changed': True,
                        'exact_match': True,
                        'message': f"语言已成功更改为 {target_locale}"
                    }
                elif target_language in current_locale.lower() and current_locale != previous_locale:
                    return {
                        'success': True,
                        'current_locale': current_locale,
                        'changed': True,
                        'exact_match': False,
                        'message': f"语言已更改为 {current_locale}（接近目标 {target_locale}）"
                    }
                elif current_locale != previous_locale:
                    return {
                        'success': False,
                        'current_locale': current_locale,
                        'changed': True,
                        'exact_match': False,
                        'message': f"语言已更改但不是目标语言：{current_locale} (目标: {target_locale})"
                    }

            # 所有尝试都失败
            final_locale = self._get_current_locale()
            return {
                'success': False,
                'current_locale': final_locale,
                'changed': final_locale != previous_locale,
                'exact_match': False,
                'message': f"语言验证失败，当前语言: {final_locale}"
            }

        except Exception as e:
            log.debug(f"验证语言更改异常: {e}")
            return {
                'success': False,
                'current_locale': 'unknown',
                'changed': False,
                'exact_match': False,
                'message': f"验证过程发生异常: {e}"
            }

    def get_supported_languages(self) -> Dict[str, Any]:
        """
        获取设备支持的语言列表

        Returns:
            Dict[str, Any]: 支持的语言信息
        """
        try:
            log.info("🌍 获取设备支持的语言列表...")

            result = {
                'success': False,
                'current_locale': '',
                'supported_locales': [],
                'common_languages': [],
                'error': ''
            }

            # 获取当前语言
            result['current_locale'] = self._get_current_locale()

            # 尝试获取系统支持的语言列表
            try:
                success, output = self.detector_utils.execute_adb_command([
                    "adb", "shell", "pm", "list", "features", "|", "grep", "locale"
                ], timeout=15)

                if success and output.strip():
                    lines = output.strip().split('\n')
                    for line in lines:
                        if 'locale' in line.lower():
                            result['supported_locales'].append(line.strip())

            except Exception as e:
                log.debug(f"获取系统语言特性失败: {e}")

            # 提供常见语言列表
            result['common_languages'] = [
                {'code': 'en-US', 'name': 'English (United States)', 'native': 'English'},
                {'code': 'en-GB', 'name': 'English (United Kingdom)', 'native': 'English (UK)'},
                {'code': 'zh-CN', 'name': 'Chinese (Simplified)', 'native': '简体中文'},
                {'code': 'zh-TW', 'name': 'Chinese (Traditional)', 'native': '繁體中文'},
                {'code': 'ja-JP', 'name': 'Japanese', 'native': '日本語'},
                {'code': 'ko-KR', 'name': 'Korean', 'native': '한국어'},
                {'code': 'fr-FR', 'name': 'French', 'native': 'Français'},
                {'code': 'de-DE', 'name': 'German', 'native': 'Deutsch'},
                {'code': 'es-ES', 'name': 'Spanish', 'native': 'Español'},
                {'code': 'it-IT', 'name': 'Italian', 'native': 'Italiano'},
                {'code': 'pt-BR', 'name': 'Portuguese (Brazil)', 'native': 'Português (Brasil)'},
                {'code': 'ru-RU', 'name': 'Russian', 'native': 'Русский'},
                {'code': 'ar-SA', 'name': 'Arabic', 'native': 'العربية'},
                {'code': 'hi-IN', 'name': 'Hindi', 'native': 'हिन्दी'},
                {'code': 'th-TH', 'name': 'Thai', 'native': 'ไทย'},
                {'code': 'vi-VN', 'name': 'Vietnamese', 'native': 'Tiếng Việt'}
            ]

            result['success'] = True
            log.info(f"✅ 成功获取语言信息，当前语言: {result['current_locale']}")

            return result

        except Exception as e:
            error_msg = f"获取支持语言列表时发生异常: {e}"
            log.error(error_msg)
            return {
                'success': False,
                'current_locale': '',
                'supported_locales': [],
                'common_languages': [],
                'error': error_msg
            }

    def format_language_result(self, result: Dict[str, Any]) -> str:
        """
        格式化语言设置结果输出

        Args:
            result: 语言设置结果字典

        Returns:
            str: 格式化后的输出
        """
        output = f"\n🌍 系统语言设置结果\n"
        output += "=" * 50 + "\n"

        if result.get('error'):
            output += f"❌ 错误: {result['error']}\n"
            return output

        if result.get('success'):
            output += f"✅ 设置状态: 成功\n"
            output += f"📝 结果消息: {result.get('message', '')}\n"

            if result.get('previous_locale'):
                output += f"🔄 语言更改: {result['previous_locale']} → {result['locale']}\n"
            else:
                output += f"🎯 目标语言: {result['locale']}\n"

            # 提供重启建议
            if "重启" in result.get('message', ''):
                output += f"\n💡 建议: 重启设备以确保语言设置完全生效\n"
                output += f"   命令: adb reboot\n"
        else:
            output += f"❌ 设置状态: 失败\n"
            if result.get('message'):
                output += f"📝 失败原因: {result['message']}\n"

        return output

    def format_supported_languages(self, result: Dict[str, Any]) -> str:
        """
        格式化支持语言列表输出

        Args:
            result: 支持语言结果字典

        Returns:
            str: 格式化后的输出
        """
        output = f"\n🌍 设备语言信息\n"
        output += "=" * 50 + "\n"

        if result.get('error'):
            output += f"❌ 错误: {result['error']}\n"
            return output

        if result.get('success'):
            current_locale = result.get('current_locale', 'unknown')
            output += f"📱 当前系统语言: {current_locale}\n"

            # 显示常见语言列表
            common_languages = result.get('common_languages', [])
            if common_languages:
                output += f"\n🎯 常见语言列表 ({len(common_languages)} 种):\n"
                output += f"{'序号':<4} {'代码':<8} {'英文名称':<25} {'本地名称':<15}\n"
                output += "-" * 60 + "\n"

                for i, lang in enumerate(common_languages, 1):
                    code = lang['code']
                    name = lang['name'][:23] + ".." if len(lang['name']) > 25 else lang['name']
                    native = lang['native'][:13] + ".." if len(lang['native']) > 15 else lang['native']

                    # 标记当前语言
                    marker = " ✅" if code == current_locale or code.split('-')[0] in current_locale else ""

                    output += f"{i:<4} {code:<8} {name:<25} {native:<15}{marker}\n"

                output += f"\n💡 使用方法: --set-language <语言代码>\n"
                output += f"   示例: --set-language en-US\n"
        else:
            output += f"❌ 获取语言信息失败\n"

        return output


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ADB进程监控工具')
    parser.add_argument('--export', type=str, help='导出结果到JSON文件')
    parser.add_argument('--filter', type=str, help='按关键词过滤进程')
    parser.add_argument('--min-memory', type=float, help='按最小内存过滤(MB)')
    parser.add_argument('--foreground-only', action='store_true', help='只显示前台进程')
    parser.add_argument('--background-only', action='store_true', help='只显示后台进程')
    parser.add_argument('--check-package', type=str, help='检测指定包名是否在前台或后台运行')
    parser.add_argument('--is-running', type=str, help='校验指定包名是否正在运行(返回True/False)')
    parser.add_argument('--install-app', type=str, help='根据应用名称安装应用，例如: --install-app WhatsApp')
    parser.add_argument('--apk-dir', type=str, default='data', help='APK文件搜索目录，默认为data（相对于项目根目录）')
    parser.add_argument('--grant-audio-permission', type=str, nargs='?', const='auto',
                        help='给录屏应用授予音频录制权限，可指定包名或使用auto自动检测')
    parser.add_argument('--check-audio-permission', type=str, nargs='?', const='auto',
                        help='检查录屏应用的音频权限状态，可指定包名或使用auto自动检测')
    parser.add_argument('--set-language', type=str, help='设置系统语言，格式: 语言代码-国家代码 (如: en-US, zh-CN)')
    parser.add_argument('--get-languages', action='store_true', help='获取设备当前语言和支持的语言列表')

    args = parser.parse_args()

    # 如果没有参数，显示帮助
    if len(sys.argv) == 1:
        log.info("📱 ADB进程监控工具")
        log.info("=" * 60)
        log.info("使用方法:")
        log.info("  python adb_process_monitor.py                    # 显示所有应用进程")
        log.info("  python adb_process_monitor.py --foreground-only  # 只显示前台进程")
        log.info("  python adb_process_monitor.py --background-only  # 只显示后台进程")
        log.info("  python adb_process_monitor.py --filter wechat    # 过滤包含wechat的进程")
        log.info("  python adb_process_monitor.py --min-memory 50    # 过滤内存大于50MB的进程")
        log.info("  python adb_process_monitor.py --export result.json # 导出结果到output目录")
        log.info("  python adb_process_monitor.py --check-package com.example.app # 检测指定包名状态")
        log.info("  python adb_process_monitor.py --is-running com.example.app # 校验包名是否运行(True/False)")
        log.info("  python adb_process_monitor.py --install-app WhatsApp # 根据应用名称安装本地APK")
        log.info("  python adb_process_monitor.py --install-app Chrome --apk-dir D:/apks # 指定本地APK目录")
        log.info("  python adb_process_monitor.py --grant-audio-permission # 自动检测并授予录屏应用音频权限")
        log.info(
            "  python adb_process_monitor.py --grant-audio-permission com.transsion.screenrecorder # 给指定录屏应用授予音频权限")
        log.info("  python adb_process_monitor.py --check-audio-permission # 自动检测并检查录屏应用音频权限状态")
        log.info(
            "  python adb_process_monitor.py --check-audio-permission com.transsion.screenrecorder # 检查指定录屏应用音频权限状态")
        log.info("  python adb_process_monitor.py --set-language en-US # 设置系统语言为英语(美国)")
        log.info("  python adb_process_monitor.py --set-language zh-CN # 设置系统语言为简体中文")
        log.info("  python adb_process_monitor.py --get-languages # 获取当前语言和支持的语言列表")
        log.info("")
        log.info("💡 功能特性:")
        log.info("- 自动区分前台和后台应用进程")
        log.info("- 显示进程PID、内存使用、运行状态")
        log.info("- 支持关键词和内存大小过滤")
        log.info("- 支持导出结果到JSON文件(自动存储到output目录)")
        log.info("- 支持检测指定包名的运行状态")
        log.info("- 支持简单的运行状态校验(True/False)")
        log.info("- 支持根据应用名称自动查找并安装本地APK文件")
        log.info("- 支持给录屏应用授予音频录制权限")
        log.info("- 支持检查录屏应用的音频权限状态")
        log.info("- 支持设置系统语言(支持多种常见语言)")
        log.info("- 支持获取设备当前语言和支持的语言列表")
        return

    try:
        monitor = AdbProcessMonitor()

        # 如果是检测指定包名状态
        if args.check_package:
            log.info(f"🔍 正在检测包名状态: {args.check_package}")
            status_info = monitor.check_package_status(args.check_package)
            print(monitor.format_package_status(status_info))

            # 如果需要导出结果
            if args.export:
                export_data = {
                    'timestamp': datetime.now().isoformat(),
                    'package_status': status_info
                }

                # 使用优化后的路径获取方法
                output_path = monitor._get_output_path(args.export)

                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                print(f"✅ 状态信息已导出到: {output_path}")

            return

        # 如果是简单的运行状态校验
        if args.is_running:
            log.info(f"🔍 正在校验包名运行状态: {args.is_running}")
            is_running = monitor.is_package_running(args.is_running)

            # 输出简洁的结果
            status_text = "True" if is_running else "False"
            status_icon = "✅" if is_running else "❌"

            print(f"\n📱 包名: {args.is_running}")
            print(f"{status_icon} 运行状态: {status_text}")

            if is_running:
                print("💡 应用正在运行 (前台或后台)")
            else:
                print("💡 应用未运行")

            # 如果需要导出结果
            if args.export:
                export_data = {
                    'timestamp': datetime.now().isoformat(),
                    'package_name': args.is_running,
                    'is_running': is_running,
                    'result': status_text
                }

                # 使用优化后的路径获取方法
                output_path = monitor._get_output_path(args.export)

                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                print(f"✅ 校验结果已导出到: {output_path}")

            return

        # 如果是应用安装请求
        if args.install_app:
            log.info(f"📦 正在处理应用安装请求: {args.install_app}")
            install_result = monitor.install_app_by_name(args.install_app, args.apk_dir)
            print(monitor.format_install_result(install_result))

            # 如果需要导出结果
            if args.export:
                export_data = {
                    'timestamp': datetime.now().isoformat(),
                    'install_result': install_result
                }

                # 使用优化后的路径获取方法
                output_path = monitor._get_output_path(args.export)

                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                print(f"✅ 安装结果已导出到: {output_path}")

            return

        # 如果是授予录屏音频权限请求
        if args.grant_audio_permission:
            package_name = None if args.grant_audio_permission == 'auto' else args.grant_audio_permission
            log.info(f"🎙️ 正在为录屏应用授予音频权限: {package_name or '自动检测'}")

            permission_result = monitor.grant_screen_recorder_audio_permission(package_name)
            print(monitor.format_permission_result(permission_result))

            # 如果需要导出结果
            if args.export:
                export_data = {
                    'timestamp': datetime.now().isoformat(),
                    'permission_result': permission_result
                }

                output_path = monitor._get_output_path(args.export)
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                print(f"✅ 权限授予结果已导出到: {output_path}")

            return

        # 如果是检查录屏音频权限请求
        if args.check_audio_permission:
            package_name = None if args.check_audio_permission == 'auto' else args.check_audio_permission
            log.info(f"🔍 正在检查录屏应用音频权限: {package_name or '自动检测'}")

            check_result = monitor.check_screen_recorder_permissions(package_name)
            print(monitor.format_permission_check_result(check_result))

            # 如果需要导出结果
            if args.export:
                export_data = {
                    'timestamp': datetime.now().isoformat(),
                    'permission_check_result': check_result
                }

                output_path = monitor._get_output_path(args.export)
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                print(f"✅ 权限检查结果已导出到: {output_path}")

            return

        # 如果是设置系统语言请求
        if args.set_language:
            log.info(f"🌍 正在设置系统语言: {args.set_language}")

            # 解析语言代码
            if '-' in args.set_language:
                language, country = args.set_language.split('-', 1)
            else:
                language = args.set_language
                country = "US" if language == "en" else "CN" if language == "zh" else "US"
                log.info(f"💡 未指定国家代码，使用默认值: {country}")

            language_result = monitor.set_system_language(language, country)
            print(monitor.format_language_result(language_result))

            # 如果需要导出结果
            if args.export:
                export_data = {
                    'timestamp': datetime.now().isoformat(),
                    'language_result': language_result
                }

                output_path = monitor._get_output_path(args.export)
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                print(f"✅ 语言设置结果已导出到: {output_path}")

            return

        # 如果是获取语言列表请求
        if args.get_languages:
            log.info("🌍 正在获取设备语言信息...")

            languages_result = monitor.get_supported_languages()
            print(monitor.format_supported_languages(languages_result))

            # 如果需要导出结果
            if args.export:
                export_data = {
                    'timestamp': datetime.now().isoformat(),
                    'languages_result': languages_result
                }

                output_path = monitor._get_output_path(args.export)
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                print(f"✅ 语言信息已导出到: {output_path}")

            return

        # 获取所有进程
        log.info("🔍 正在获取设备进程信息...")
        all_processes = monitor.get_all_processes()

        if not all_processes:
            log.error("❌ 未能获取到进程信息，请检查设备连接")
            return

        # 分类前台和后台进程
        foreground_processes, background_processes = monitor.get_app_processes(all_processes)

        # 应用过滤器
        if args.filter or args.min_memory:
            log.info(f"🔍 应用过滤器: 关键词='{args.filter}', 最小内存={args.min_memory}MB")
            foreground_processes = monitor.filter_processes(
                foreground_processes, args.filter, args.min_memory
            )
            background_processes = monitor.filter_processes(
                background_processes, args.filter, args.min_memory
            )

        # 显示结果
        if not args.background_only:
            print(monitor.format_process_info(foreground_processes, "前台"))

        if not args.foreground_only:
            print(monitor.format_process_info(background_processes, "后台"))

        # 显示统计信息
        total_count = len(foreground_processes) + len(background_processes)
        print(f"\n📊 统计信息:")
        print(f"前台进程: {len(foreground_processes)} 个")
        print(f"后台进程: {len(background_processes)} 个")
        print(f"总计: {total_count} 个应用进程")

        # 导出结果
        if args.export:
            success = monitor.export_to_json(
                foreground_processes, background_processes, args.export
            )
            if success:
                print(f"✅ 结果已导出到: {args.export}")
            else:
                print(f"❌ 导出失败")

    except KeyboardInterrupt:
        log.info("\n👋 用户中断操作")
    except Exception as e:
        log.error(f"❌ 运行出错: {e}")


if __name__ == '__main__':
    # 运行主函数
    # main()

    # 测试代码（注释掉，避免与主函数冲突）
    # monitor = AdbProcessMonitor()
    # result = monitor.is_package_running(package_name='com.gallery20')
    # monitor.clear_all_running_processes()
    # result = monitor.is_package_running(package_name='com.transsion.screenrecorder')
    # result = monitor.is_package_actively_running(package_name='com.transsion.screenrecorder')
    # result = monitor.is_package_running_fast(package_name='com.transsion.screenrecorder')
    # print(result)

    # 测试应用安装功能
    # monitor = AdbProcessMonitor()
    # result = monitor.install_app_by_name("whatsapp", r"D:\aigc\app_test\data")
    # print(monitor.format_install_result(result))

    # 测试录屏音频权限功能
    # monitor = AdbProcessMonitor()
    #
    # # 检查权限状态
    # check_result = monitor.check_screen_recorder_permissions()
    # print(monitor.format_permission_check_result(check_result))
    #
    # # 授予权限
    # grant_result = monitor.grant_screen_recorder_audio_permission()
    # print(monitor.format_permission_result(grant_result))

    # 测试语言设置功能
    monitor = AdbProcessMonitor()
    # result = monitor.set_system_language("zh", "CN")
    # result = monitor.set_system_language()
    # print(monitor.format_language_result(result))


# ==================== 便捷函数 ====================

def clear_app(package_name: str, force_stop: bool = True, clear_data: bool = False) -> bool:
    """
    清理指定包名应用的便捷函数

    Args:
        package_name: 应用包名，如 'com.android.chrome'
        force_stop: 是否强制停止应用，默认True
        clear_data: 是否清除应用数据，默认False（谨慎使用）

    Returns:
        bool: 清理是否成功

    Example:
        # 清理Chrome浏览器
        clear_app('com.android.chrome')

        # 清理应用并清除数据
        clear_app('com.example.app', clear_data=True)
    """
    monitor = AdbProcessMonitor()
    return monitor.clear_specific_app(package_name, force_stop, clear_data)


def clear_multiple_apps(package_names: list, force_stop: bool = True, clear_data: bool = False) -> dict:
    """
    批量清理多个指定包名应用的便捷函数

    Args:
        package_names: 应用包名列表
        force_stop: 是否强制停止应用，默认True
        clear_data: 是否清除应用数据，默认False（谨慎使用）

    Returns:
        dict: 清理结果统计

    Example:
        # 批量清理多个应用
        apps = ['com.android.chrome', 'com.google.android.apps.maps', 'com.facebook.katana']
        result = clear_multiple_apps(apps)
        print(f"清理结果: 成功 {result['success_count']}/{result['total']} 个应用")
    """
    monitor = AdbProcessMonitor()
    return monitor.clear_multiple_apps(package_names, force_stop, clear_data)


def clear_common_apps(include_social: bool = True, include_browsers: bool = True,
                     include_maps: bool = True, clear_data: bool = False) -> dict:
    """
    清理常用应用的便捷函数

    Args:
        include_social: 是否包含社交应用，默认True
        include_browsers: 是否包含浏览器应用，默认True
        include_maps: 是否包含地图应用，默认True
        clear_data: 是否清除应用数据，默认False（谨慎使用）

    Returns:
        dict: 清理结果统计

    Example:
        # 清理所有常用应用
        result = clear_common_apps()

        # 只清理浏览器和地图应用
        result = clear_common_apps(include_social=False)
    """
    apps_to_clear = []

    if include_browsers:
        apps_to_clear.extend([
            "com.android.chrome",  # Chrome浏览器
            "com.microsoft.emmx",  # Edge浏览器
            "org.mozilla.firefox",  # Firefox浏览器
        ])

    if include_social:
        apps_to_clear.extend([
            "com.facebook.katana",  # Facebook
            "com.whatsapp",  # WhatsApp
            "com.tencent.mm",  # 微信
            "com.instagram.android",  # Instagram
            "com.twitter.android",  # Twitter
        ])

    if include_maps:
        apps_to_clear.extend([
            "com.google.android.apps.maps",  # Google Maps
            "com.autonavi.minimap",  # 高德地图
            "com.baidu.BaiduMap",  # 百度地图
        ])

    monitor = AdbProcessMonitor()
    return monitor.clear_multiple_apps(apps_to_clear, force_stop=True, clear_data=clear_data)
