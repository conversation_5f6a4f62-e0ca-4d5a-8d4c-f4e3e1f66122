{"uid": "b80d3692e1f30339", "name": "测试What's the weather like today能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_s_the_weather_like_today.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_today", "historyId": "ac6792be4ebb553a5655a2c44aca218c", "time": {"start": 1756215274292, "stop": 1756215297388, "duration": 23096}, "description": "What's the weather like today", "descriptionHtml": "<p>What's the weather like today</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.dialogue.test_what_s_the_weather_like_today.TestEllaWhatSWeatherLikeShanghaiToday object at 0x0000023971EFABD0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000023973C7FA90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_s_the_weather_like_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_what_s_the_weather_like_today.py:37: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": 1756211311302, "stop": 1756211311302, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment", "time": {"start": 1756211311303, "stop": 1756211311518, "duration": 215}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1756215261772, "stop": 1756215274291, "duration": 12519}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756215274291, "stop": 1756215274291, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "What's the weather like today", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.dialogue.test_what_s_the_weather_like_today.TestEllaWhatSWeatherLikeShanghaiToday object at 0x0000023971EFABD0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000023973C7FA90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_s_the_weather_like_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_what_s_the_weather_like_today.py:37: AssertionError", "steps": [{"name": "执行命令: What's the weather like today", "time": {"start": 1756215274292, "stop": 1756215297385, "duration": 23093}, "status": "passed", "steps": [{"name": "执行命令: What's the weather like today", "time": {"start": 1756215274292, "stop": 1756215297200, "duration": 22908}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1756215297200, "stop": 1756215297385, "duration": 185}, "status": "passed", "steps": [], "attachments": [{"uid": "acb8ae737cf4ede0", "name": "测试总结", "source": "acb8ae737cf4ede0.txt", "type": "text/plain", "size": 328}, {"uid": "b080fdb1e216f1b", "name": "test_completed", "source": "b080fdb1e216f1b.png", "type": "image/png", "size": 591577}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1756215297385, "stop": 1756215297387, "duration": 2}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\dialogue\\test_what_s_the_weather_like_today.py\", line 37, in test_what_s_the_weather_like_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "bc3368afb5497fd6", "name": "stdout", "source": "bc3368afb5497fd6.txt", "type": "text/plain", "size": 14088}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "stepsCount": 4, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756215297394, "stop": 1756215297570, "duration": 176}, "status": "passed", "steps": [], "attachments": [{"uid": "3a03d3cefee568d7", "name": "失败截图-TestEllaWhatSWeatherLikeShanghaiToday", "source": "3a03d3cefee568d7.png", "type": "image/png", "size": 592220}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "stepsCount": 0, "hasContent": true, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1756215297573, "stop": 1756215298872, "duration": 1299}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1756227892081, "stop": 1756227892082, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_weather_like_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "88492-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_weather_like_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "b80d3692e1f30339.json", "parameterValues": []}