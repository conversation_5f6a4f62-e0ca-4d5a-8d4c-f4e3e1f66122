--- Logging error in <PERSON><PERSON><PERSON> Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9792, microseconds=487841), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'setup_batch_test_screen', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 64, 'message': '📱 批量测试屏幕设置已完成，跳过重复设置', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 26222, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9792, microseconds=568081), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': '_init_detectors', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 469, 'message': '✅ 使用绝对路径导入成功 - 所有检测器', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 106462, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9792, microseconds=568081), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': '_init_detectors', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 586, 'message': '✅ 成功初始化 83 个检测器', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 106462, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9792, microseconds=570086), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_load_process_cleanup_config', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1758, 'message': '✅ 已加载进程清理配置，共 3 项', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 108467, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9792, microseconds=570086), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_command_clear_all_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1872, 'message': '⚡ 执行命令直接清理...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 108467, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9792, microseconds=570086), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_stop_running_apps_by_list', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1901, 'message': '📋 获取运行应用列表并逐个停止...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 108467, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9792, microseconds=938398), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1975, 'message': '🔧 执行系统级清理命令...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 476779, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9793, microseconds=59090), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: am kill-all', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 597471, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9793, microseconds=164197), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: pm trim-caches 1000M', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 702578, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9793, microseconds=351589), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: rm -rf /data/local/tmp/*', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 889970, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9793, microseconds=352609), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2008, 'message': '📱 按类别清理应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 890990, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9793, microseconds=450873), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Facebook', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 34, 989254, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9793, microseconds=542580), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Facebook Lite', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 35, 80961, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9793, microseconds=648777), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: WhatsApp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 35, 187158, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9793, microseconds=760186), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Instagram', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 35, 298567, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9793, microseconds=870009), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Twitter', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 35, 408390, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9793, microseconds=964318), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: 微信', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 35, 502699, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9794, microseconds=66698), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: QQ', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 35, 605079, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9794, microseconds=175104), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: 微博', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 35, 713485, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9794, microseconds=279244), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: Spotify', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 35, 817625, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9794, microseconds=385679), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: Netflix', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 35, 924060, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9794, microseconds=477404), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: 抖音', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 36, 15785, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9794, microseconds=597356), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: 快手', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 36, 135737, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9794, microseconds=705278), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理game应用: 王者荣耀', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 36, 243659, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9794, microseconds=817893), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理game应用: 和平精英', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 36, 356274, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9794, microseconds=927716), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理browser应用: Chrome浏览器', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 36, 466097, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9795, microseconds=62957), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理browser应用: UC浏览器', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 36, 601338, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9795, microseconds=62957), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_command_clear_all_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1888, 'message': '✅ 命令清理完成，清理了 22 个应用', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 36, 601338, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9795, microseconds=63957), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2054, 'message': '💪 强制停止顽固应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 36, 602338, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9795, microseconds=169297), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.google.android.apps.maps', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 36, 707678, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9795, microseconds=605980), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.google.android.apps.maps', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 37, 144361, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9795, microseconds=786601), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.google.android.gms', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 37, 324982, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9796, microseconds=230837), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.google.android.gms', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 37, 769218, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9796, microseconds=367441), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.android.chrome', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 37, 905822, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9796, microseconds=791341), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.android.chrome', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 38, 329722, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9796, microseconds=906166), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.facebook.katana', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 38, 444547, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9797, microseconds=358912), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.facebook.katana', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 38, 897293, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9797, microseconds=454025), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.whatsapp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 38, 992406, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9797, microseconds=844332), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.whatsapp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 39, 382713, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9797, microseconds=955705), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.tencent.mm', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 39, 494086, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9798, microseconds=397047), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.tencent.mm', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 39, 935428, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9798, microseconds=494326), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.netflix.mediaclient', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 40, 32707, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9798, microseconds=902623), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.netflix.mediaclient', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 40, 441004, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9799, microseconds=11745), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.spotify.music', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 40, 550126, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9799, microseconds=431508), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.spotify.music', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 40, 969889, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9799, microseconds=529640), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2111, 'message': '🧹 执行系统内存清理', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 41, 68021, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9799, microseconds=643974), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2118, 'message': '🗑️ 执行垃圾回收', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 41, 182355, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9805, microseconds=235722), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 46, 774103, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9805, microseconds=287763), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_load_status_check_config', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 287, 'message': '✅ 已加载状态检查配置，共 38 项', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 46, 826144, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9805, microseconds=287763), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_detect_command_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 621, 'message': '未识别的命令类型: change (female/tone name) voice', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 46, 826144, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9805, microseconds=476499), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': '检查对话页面指示器...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 47, 14880, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9805, microseconds=514922), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 47, 53303, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9805, microseconds=560869), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 812, 'message': '✅ 找到主输入框', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 47, 99250, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9805, microseconds=589398), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 47, 127779, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9805, microseconds=677925), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 47, 216306, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9805, microseconds=756880), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 47, 295261, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9806, microseconds=9149), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 47, 547530, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9806, microseconds=469507), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [发送按钮]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 48, 7888, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9806, microseconds=766280), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_popup_tool', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 164, 'message': '✅ 弹窗处理工具初始化成功', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 48, 304661, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9807, microseconds=707144), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_handle_popup_after_command', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 193, 'message': 'ℹ️ 未检测到弹窗或无需处理，命令: change (female/tone name) voice', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 49, 245525, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9807, microseconds=933488), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'11:11 pm\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', \'David Luiz Denies Infidelity Claims\', "Butler\'s Warriors Future in Doubt", \'How to use Ask About Screen\']...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 49, 471869, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9808, microseconds=179699), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'11:11 pm\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', \'David Luiz Denies Infidelity Claims\', "Butler\'s Warriors Future in Doubt", \'How to use Ask About Screen\']...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 49, 718080, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9808, microseconds=311402), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_detect_command_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 621, 'message': '未识别的命令类型: change (female/tone name) voice', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 49, 849783, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9808, microseconds=312430), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_final_status_with_page_info', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 432, 'message': '等待状态变化: 3.0秒', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 49, 850811, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9811, microseconds=692084), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': '检查对话页面指示器...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 53, 230465, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9811, microseconds=717772), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 53, 256153, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9811, microseconds=737722), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 812, 'message': '✅ 找到主输入框', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 53, 276103, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9812, microseconds=116353), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从asr_txt节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 53, 654734, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9812, microseconds=257691), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从asr_txt节点获取到原始文本: "change (female/tone name) voice"', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 53, 796072, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9812, microseconds=257691), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'change (female/tone name) voice'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 53, 796072, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9812, microseconds=258677), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: change (female/tone name) voice', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 53, 797058, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9812, microseconds=258677), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从robot_text节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 53, 797058, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9812, microseconds=284110), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'robot_text节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 53, 822491, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9812, microseconds=785011), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从robot_text节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 54, 323392, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9812, microseconds=812213), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'robot_text节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 54, 350594, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9813, microseconds=314128), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从robot_text节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 54, 852509, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9813, microseconds=434105), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'robot_text节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 54, 972486, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9813, microseconds=435089), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 54, 973470, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9813, microseconds=482589), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 55, 20970, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9813, microseconds=983583), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 55, 521964, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9814, microseconds=85467), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 55, 623848, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9814, microseconds=585922), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 56, 124303, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9814, microseconds=685732), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 56, 224113, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9814, microseconds=686731), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 56, 225112, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9814, microseconds=742971), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 56, 281352, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9815, microseconds=243537), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 56, 781918, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9815, microseconds=337343), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 56, 875724, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9815, microseconds=838237), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 57, 376618, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9815, microseconds=901613), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 57, 439994, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9815, microseconds=901613), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 57, 439994, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9815, microseconds=936612), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 57, 474993, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9816, microseconds=437892), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 57, 976273, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9816, microseconds=541212), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 58, 79593, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9817, microseconds=42553), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 58, 580934, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9817, microseconds=170259), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 58, 708640, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9817, microseconds=171259), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 58, 709640, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9817, microseconds=225573), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 58, 763954, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9817, microseconds=726614), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 59, 264995, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9817, microseconds=817619), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 59, 356000, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9818, microseconds=318755), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 59, 857136, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9818, microseconds=399906), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 59, 938287, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9818, microseconds=401705), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 11, 59, 940086, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9818, microseconds=466290), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 0, 4671, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9818, microseconds=967260), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 0, 505641, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9819, microseconds=32466), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 0, 570847, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9819, microseconds=533457), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 1, 71838, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9819, microseconds=675420), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 1, 213801, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9819, microseconds=676430), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_text节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 1, 214811, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9819, microseconds=761771), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从tv_text节点获取到原始文本: "I can answer your questions, summarize content, and provide creative inspiration."', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 1, 300152, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9819, microseconds=761771), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'I can answer your questions, summarize content, and provide creative inspiration.'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 1, 300152, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9819, microseconds=761771), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: I can answer your questions, summarize content, and provide creative inspiration.', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 1, 300152, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9819, microseconds=762786), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 1, 301167, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9819, microseconds=791021), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 1, 329402, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9820, microseconds=291719), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 1, 830100, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9820, microseconds=366549), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 1, 904930, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9820, microseconds=867742), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 2, 406123, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9820, microseconds=953646), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 2, 492027, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9820, microseconds=953646), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 2, 492027, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9820, microseconds=995206), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 2, 533587, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9821, microseconds=496984), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 3, 35365, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9821, microseconds=568880), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 3, 107261, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9822, microseconds=70224), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 3, 608605, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9822, microseconds=142962), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 3, 681343, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9822, microseconds=144980), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 3, 683361, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9822, microseconds=176065), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 3, 714446, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9822, microseconds=677349), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 4, 215730, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9822, microseconds=763106), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 4, 301487, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9823, microseconds=264630), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 4, 803011, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9823, microseconds=418234), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 4, 956615, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9823, microseconds=614727), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'Dialogue'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 5, 153108, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9823, microseconds=614727), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: Dialogue', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 5, 153108, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9823, microseconds=802550), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 16 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'11:11 pm\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', \'David Luiz Denies Infidelity Claims\', "Butler\'s Warriors Future in Doubt", \'How to use Ask About Screen\']...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 5, 340931, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9823, microseconds=982625), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1030, 'message': '响应文本(列表转换): 原始列表=[\'change (female/tone name) voice\', \'\', \'\', \'\', \'\', \'\', \'\', \'I can answer your questions, summarize content, and provide creative inspiration.\', \'\', \'\', \'\', \'Dialogue\', "Dialogue Explore Swipe down to view earlier chats 11:11 pm Hi, I\'m Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh David Luiz Denies Infidelity Claims Butler\'s Warriors Future in Doubt How to use Ask About Screen change (female/tone name) voice Generating image… 35% Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate 11:12"], 过滤后=[\'change (female/tone name) voice\', \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Dialogue\', "Dialogue Explore Swipe down to view earlier chats 11:11 pm Hi, I\'m Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh David Luiz Denies Infidelity Claims Butler\'s Warriors Future in Doubt How to use Ask About Screen change (female/tone name) voice Generating image… 35% Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate 11:12"], 合并后=change (female/tone name) voice I can answer your questions, summarize content, and provide creative inspiration. Dialogue Dialogue Explore Swipe down to view earlier chats 11:11 pm Hi, I\'m Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh David Luiz Denies Infidelity Claims Butler\'s Warriors Future in Doubt How to use Ask About Screen change (female/tone name) voice Generating image… 35% Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate 11:12', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 5, 521006, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=9825, microseconds=444010), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_verify_app_stopped', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 426, 'message': '当前前台应用: com.android.vending，目标应用已不在前台', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 12, 6, 982391, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
