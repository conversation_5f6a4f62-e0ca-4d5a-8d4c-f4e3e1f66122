--- Logging error in <PERSON><PERSON><PERSON> Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10739, microseconds=400504), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'setup_batch_test_screen', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 64, 'message': '📱 批量测试屏幕设置已完成，跳过重复设置', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 20, 938885, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10739, microseconds=490445), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': '_init_detectors', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 469, 'message': '✅ 使用绝对路径导入成功 - 所有检测器', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 28826, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10739, microseconds=491444), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': '_init_detectors', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 586, 'message': '✅ 成功初始化 83 个检测器', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 29825, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10739, microseconds=494444), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_load_process_cleanup_config', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1758, 'message': '✅ 已加载进程清理配置，共 3 项', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 32825, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10739, microseconds=495445), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_command_clear_all_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1872, 'message': '⚡ 执行命令直接清理...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 33826, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10739, microseconds=495445), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_stop_running_apps_by_list', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1901, 'message': '📋 获取运行应用列表并逐个停止...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 33826, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10739, microseconds=788461), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1975, 'message': '🔧 执行系统级清理命令...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 326842, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10739, microseconds=923321), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: am kill-all', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 461702, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10740, microseconds=34533), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: pm trim-caches 1000M', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 572914, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10740, microseconds=223149), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: rm -rf /data/local/tmp/*', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 761530, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10740, microseconds=223149), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2008, 'message': '📱 按类别清理应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 761530, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10740, microseconds=351609), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Facebook', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 889990, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10740, microseconds=449366), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Facebook Lite', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 21, 987747, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10740, microseconds=547436), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: WhatsApp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 22, 85817, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10740, microseconds=647221), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Instagram', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 22, 185602, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10740, microseconds=745727), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Twitter', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 22, 284108, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10740, microseconds=840921), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: 微信', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 22, 379302, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10740, microseconds=938878), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: QQ', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 22, 477259, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=42362), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: 微博', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 22, 580743, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=143275), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: Spotify', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 22, 681656, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=246611), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: Netflix', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 22, 784992, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=375111), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: 抖音', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 22, 913492, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=485653), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: 快手', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 23, 24034, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=578240), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理game应用: 王者荣耀', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 23, 116621, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=676731), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理game应用: 和平精英', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 23, 215112, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=802098), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理browser应用: Chrome浏览器', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 23, 340479, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=897079), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理browser应用: UC浏览器', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 23, 435460, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=897079), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_command_clear_all_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1888, 'message': '✅ 命令清理完成，清理了 22 个应用', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 23, 435460, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10741, microseconds=898079), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2054, 'message': '💪 强制停止顽固应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 23, 436460, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10742, microseconds=12361), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.google.android.apps.maps', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 23, 550742, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10742, microseconds=455999), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.google.android.apps.maps', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 23, 994380, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10742, microseconds=636810), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.google.android.gms', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 24, 175191, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10743, microseconds=72495), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.google.android.gms', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 24, 610876, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10743, microseconds=180666), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.android.chrome', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 24, 719047, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10743, microseconds=582892), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.android.chrome', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 25, 121273, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10743, microseconds=687167), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.facebook.katana', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 25, 225548, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10744, microseconds=86159), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.facebook.katana', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 25, 624540, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10744, microseconds=167510), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.whatsapp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 25, 705891, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10744, microseconds=583498), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.whatsapp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 26, 121879, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10744, microseconds=712205), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.tencent.mm', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 26, 250586, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10745, microseconds=101060), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.tencent.mm', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 26, 639441, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10745, microseconds=183062), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.netflix.mediaclient', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 26, 721443, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10745, microseconds=574184), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.netflix.mediaclient', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 27, 112565, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10745, microseconds=681448), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.spotify.music', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 27, 219829, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10746, microseconds=106163), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.spotify.music', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 27, 644544, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10746, microseconds=215682), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2111, 'message': '🧹 执行系统内存清理', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 27, 754063, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10746, microseconds=312542), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2118, 'message': '🗑️ 执行垃圾回收', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 27, 850923, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10751, microseconds=898632), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 33, 437013, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10751, microseconds=951169), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_load_status_check_config', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 287, 'message': '✅ 已加载状态检查配置，共 38 项', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 33, 489550, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10751, microseconds=951169), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_detect_command_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 618, 'message': '检测到命令类型: google_playstore (google playstore应用状态)', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 33, 489550, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10752, microseconds=57993), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_status_by_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 727, 'message': '获取google playstore应用状态(初始): True', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 33, 596374, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10752, microseconds=290204), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': '检查对话页面指示器...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 33, 828585, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10752, microseconds=319578), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 33, 857959, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10752, microseconds=348114), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 812, 'message': '✅ 找到主输入框', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 33, 886495, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10752, microseconds=385897), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 33, 924278, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10752, microseconds=463898), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 34, 2279, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10752, microseconds=523922), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 34, 62303, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10752, microseconds=757718), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 34, 296099, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10753, microseconds=125792), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [发送按钮]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 34, 664173, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10753, microseconds=523775), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_popup_tool', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 164, 'message': '✅ 弹窗处理工具初始化成功', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 35, 62156, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10754, microseconds=418162), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_handle_popup_after_command', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 193, 'message': 'ℹ️ 未检测到弹窗或无需处理，命令: download in play store', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 35, 956543, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10754, microseconds=656117), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'11:27 pm\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', "BYD\'s EV Surge & Global Ambitions", \'Rangers, Lille Reopen Igamane Talks\', \'Switch to a male voice\']...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 36, 194498, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10754, microseconds=851253), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'11:27 pm\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', "BYD\'s EV Surge & Global Ambitions", \'Rangers, Lille Reopen Igamane Talks\', \'Switch to a male voice\']...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 36, 389634, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10754, microseconds=987146), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_detect_command_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 618, 'message': '检测到命令类型: google_playstore (google playstore应用状态)', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 36, 525527, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10754, microseconds=987146), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_final_status_with_page_info', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 432, 'message': '等待状态变化: 3.0秒', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 36, 525527, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10758, microseconds=579799), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_status_by_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 727, 'message': '获取google playstore应用状态(最终): True', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 40, 118180, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10762, microseconds=466034), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 4415, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10762, microseconds=486709), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': '检查对话页面指示器...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 25090, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10762, microseconds=517293), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 55674, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10762, microseconds=560724), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 812, 'message': '✅ 找到主输入框', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 99105, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=79528), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从asr_txt节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 617909, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=199283), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从asr_txt节点获取到原始文本: "download in play store"', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 737664, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=199283), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'download in play store'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 737664, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=200698), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: download in play store', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 739079, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=200698), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从robot_text节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 739079, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=374065), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从robot_text节点获取到原始文本: "Done!"', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 912446, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=374065), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'Done!'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 912446, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=375053), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1164, 'message': '✅ 匹配到完成响应模式: done -> Done!', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 913434, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=376049), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 914430, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=459459), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 44, 997840, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=961116), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 45, 499497, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10763, microseconds=995240), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 45, 533621, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10764, microseconds=496082), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 46, 34463, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10764, microseconds=536712), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 46, 75093, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10764, microseconds=538968), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 46, 77349, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10764, microseconds=576439), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 46, 114820, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10765, microseconds=77670), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 46, 616051, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10765, microseconds=115235), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 46, 653616, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10765, microseconds=616853), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 47, 155234, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10765, microseconds=660318), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 47, 198699, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10765, microseconds=661318), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 47, 199699, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10765, microseconds=703317), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 47, 241698, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10766, microseconds=204840), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 47, 743221, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10766, microseconds=241559), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 47, 779940, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10766, microseconds=742799), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 48, 281180, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10766, microseconds=789431), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 48, 327812, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10766, microseconds=790432), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 48, 328813, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10766, microseconds=846567), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 48, 384948, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10767, microseconds=347397), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 48, 885778, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10767, microseconds=419932), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 48, 958313, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10767, microseconds=920821), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 49, 459202, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10767, microseconds=959439), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 49, 497820, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10767, microseconds=959439), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 49, 497820, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10768, microseconds=8438), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 49, 546819, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10768, microseconds=509354), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 50, 47735, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10768, microseconds=554516), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 50, 92897, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10769, microseconds=55873), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 50, 594254, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10769, microseconds=110523), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 50, 648904, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10769, microseconds=110523), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_text节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 50, 648904, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10769, microseconds=296265), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从tv_text节点获取到原始文本: "I can answer your questions, summarize content, and provide creative inspiration."', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 50, 834646, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10769, microseconds=296265), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'I can answer your questions, summarize content, and provide creative inspiration.'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 50, 834646, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10769, microseconds=296265), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: I can answer your questions, summarize content, and provide creative inspiration.', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 50, 834646, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10769, microseconds=297250), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 50, 835631, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10769, microseconds=353939), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 50, 892320, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10769, microseconds=854969), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 51, 393350, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10769, microseconds=906793), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 51, 445174, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10770, microseconds=408267), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 51, 946648, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10770, microseconds=454801), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 51, 993182, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10770, microseconds=455804), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 51, 994185, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10770, microseconds=495644), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 52, 34025, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10770, microseconds=996672), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 52, 535053, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10771, microseconds=35247), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 52, 573628, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10771, microseconds=536797), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 53, 75178, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10771, microseconds=565954), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 53, 104335, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10771, microseconds=565954), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 53, 104335, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10771, microseconds=599393), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 53, 137774, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10772, microseconds=100269), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 53, 638650, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10772, microseconds=161621), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 53, 700002, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10772, microseconds=662785), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 54, 201166, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10772, microseconds=701102), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 54, 239483, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10772, microseconds=896697), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1030, 'message': "响应文本(列表转换): 原始列表=['download in play store', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.vending页面内容] Sign in to find the latest Android apps, games, movies, music, & more'], 过滤后=['download in play store', 'Done!', 'I can answer your questions, summarize content, and provide creative inspiration.', '[com.android.vending页面内容] Sign in to find the latest Android apps, games, movies, music, & more'], 合并后=download in play store Done! I can answer your questions, summarize content, and provide creative inspiration. [com.android.vending页面内容] Sign in to find the latest Android apps, games, movies, music, & more", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 54, 435078, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=10774, microseconds=376637), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_verify_app_stopped', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 426, 'message': '当前前台应用: com.android.vending，目标应用已不在前台', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 27, 55, 915018, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
