--- Logging error in <PERSON><PERSON><PERSON> Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=604730), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'setup_batch_test_screen', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 64, 'message': '📱 批量测试屏幕设置已完成，跳过重复设置', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 143111, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=609455), 'exception': None, 'extra': {}, 'file': (name='base_page.py', path='D:\\aigc\\app_test\\core\\base_page.py'), 'function': '__init__', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 38, 'message': '初始化页面: ella - dialogue_page', 'module': 'base_page', 'name': 'core.base_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 147836, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=706809), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': '_init_detectors', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 469, 'message': '✅ 使用绝对路径导入成功 - 所有检测器', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 245190, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=707825), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 299, 'message': '🧹 开始清除手机上所有运行中的应用进程...', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 246206, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=707825), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': '_init_detectors', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 586, 'message': '✅ 成功初始化 83 个检测器', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 246206, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=708836), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1825, 'message': '🧹 开始清除手机上所有运行中的应用进程...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 247217, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=709837), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_load_process_cleanup_config', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1758, 'message': '✅ 已加载进程清理配置，共 3 项', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 248218, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=709837), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1841, 'message': '⚡ 优先使用命令直接清理...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 248218, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=709837), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_command_clear_all_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1872, 'message': '⚡ 执行命令直接清理...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 248218, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=709837), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_stop_running_apps_by_list', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1901, 'message': '📋 获取运行应用列表并逐个停止...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 248218, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15335, microseconds=999557), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1975, 'message': '🔧 执行系统级清理命令...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 537938, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15336, microseconds=117019), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: am kill-all', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 655400, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15336, microseconds=221798), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: pm trim-caches 1000M', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 760179, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15336, microseconds=439296), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: rm -rf /data/local/tmp/*', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 977677, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15336, microseconds=439296), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2008, 'message': '📱 按类别清理应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 57, 977677, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15336, microseconds=547907), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Facebook', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 58, 86288, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15336, microseconds=669206), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Facebook Lite', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 58, 207587, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15336, microseconds=781594), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: WhatsApp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 58, 319975, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15336, microseconds=883681), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Instagram', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 58, 422062, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15337, microseconds=1029), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Twitter', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 58, 539410, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15337, microseconds=99855), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: 微信', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 58, 638236, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15337, microseconds=199657), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: QQ', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 58, 738038, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15337, microseconds=289723), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: 微博', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 58, 828104, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15337, microseconds=384840), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: Spotify', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 58, 923221, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15337, microseconds=487640), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: Netflix', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 26021, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15337, microseconds=600383), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: 抖音', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 138764, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15337, microseconds=735243), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: 快手', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 273624, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15337, microseconds=845521), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理game应用: 王者荣耀', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 383902, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15337, microseconds=945901), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理game应用: 和平精英', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 484282, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15338, microseconds=73094), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理browser应用: Chrome浏览器', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 611475, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15338, microseconds=180350), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理browser应用: UC浏览器', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 718731, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15338, microseconds=181353), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1847, 'message': '💪 强制停止顽固应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 719734, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15338, microseconds=181353), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_command_clear_all_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1888, 'message': '✅ 命令清理完成，清理了 22 个应用', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 719734, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15338, microseconds=181353), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2054, 'message': '💪 强制停止顽固应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 719734, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15338, microseconds=298351), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.google.android.apps.maps', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 43, 59, 836732, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15338, microseconds=758178), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.google.android.apps.maps', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 0, 296559, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15338, microseconds=905433), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.google.android.gms', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 0, 443814, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15339, microseconds=329425), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.google.android.gms', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 0, 867806, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15339, microseconds=453188), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.android.chrome', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 0, 991569, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15339, microseconds=886829), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.android.chrome', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 1, 425210, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15339, microseconds=994022), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.facebook.katana', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 1, 532403, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15340, microseconds=402040), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.facebook.katana', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 1, 940421, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15340, microseconds=539432), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.whatsapp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 2, 77813, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15340, microseconds=968116), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.whatsapp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 2, 506497, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15341, microseconds=70689), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.tencent.mm', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 2, 609070, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15341, microseconds=452714), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.tencent.mm', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 2, 991095, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15341, microseconds=550370), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.netflix.mediaclient', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 3, 88751, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15341, microseconds=928432), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.netflix.mediaclient', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 3, 466813, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15342, microseconds=19667), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.spotify.music', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 3, 558048, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15342, microseconds=438236), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.spotify.music', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 3, 976617, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15342, microseconds=561518), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2111, 'message': '🧹 执行系统内存清理', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 4, 99899, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15342, microseconds=662922), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2118, 'message': '🗑️ 执行垃圾回收', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 4, 201303, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15342, microseconds=663912), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1857, 'message': '🎉 应用进程清理完成，共清理 30 个应用', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 4, 202293, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15344, microseconds=664980), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 301, 'message': '✅ 应用进程清理完成', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 6, 203361, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15344, microseconds=664980), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'start_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 214, 'message': '启动Ella应用', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 6, 203361, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15344, microseconds=755645), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'start_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 222, 'message': '尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 6, 294026, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=17882), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_app_started', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 280, 'message': '✅ 应用已在前台: com.transsion.aivoiceassistant', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 556263, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=18888), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'start_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 227, 'message': '✅ Ella应用启动成功（指定Activity）', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 557269, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=18888), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'wait_for_page_load', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 301, 'message': '等待Ella页面加载完成 (超时: 15秒)', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 557269, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=263171), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'wait_for_page_load', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 311, 'message': '✅ 确认当前在Ella应用中', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 801552, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=297481), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 835862, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=322819), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'wait_for_page_load', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 320, 'message': '✅ 主输入框已出现，页面加载完成', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 861200, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=324819), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_load_status_check_config', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 287, 'message': '✅ 已加载状态检查配置，共 38 项', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 863200, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=322819), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'ella_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 333, 'message': '✅ Ella应用启动成功', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 861200, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=324819), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_detect_command_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 621, 'message': '未识别的命令类型: set split-screen apps', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 863200, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=324819), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'execute_command_and_verify', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 366, 'message': '初始状态None- 使用命令set split-screen apps，状态: ', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 863200, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=325820), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 769, 'message': '确保在对话页面...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 864201, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=325820), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 9, 864201, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=515486), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': '检查对话页面指示器...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 53867, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=514485), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 52866, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=515486), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 53867, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=515486), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 53867, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=550386), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 88767, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=578457), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 812, 'message': '✅ 找到主输入框', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 116838, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=579456), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 780, 'message': '✅ 已在对话页面', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 117837, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=579456), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_ensure_input_box_ready', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 130, 'message': '确保输入框就绪...', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 117837, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=613642), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 152023, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=640167), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_check_known_input_elements', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 160, 'message': '✅ 找到主输入框', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 178548, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=640167), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': 'execute_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 34, 'message': '执行文本命令: set split-screen apps', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 178548, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=641182), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_ensure_input_box_ready', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 130, 'message': '确保输入框就绪...', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 179563, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=671751), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 210132, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=693740), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_check_known_input_elements', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 160, 'message': '✅ 找到主输入框', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 232121, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=693740), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_input_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 230, 'message': '输入文本命令: set split-screen apps', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 232121, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=722882), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 261263, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=761702), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 62, 'message': '等待元素出现 [输入框], 超时时间: 5秒', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 300083, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=803032), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 65, 'message': '元素列表 [True]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 341413, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=804029), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 67, 'message': '元素已出现 [输入框]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 342410, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=901527), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'clear_text', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 325, 'message': '清空文本成功 [输入框]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 439908, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=931816), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 470197, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=965470), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 62, 'message': '等待元素出现 [输入框], 超时时间: 5秒', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 503851, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=997657), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 65, 'message': '元素列表 [True]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 536038, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15348, microseconds=997657), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 67, 'message': '元素已出现 [输入框]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 536038, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=191915), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'send_keys', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 302, 'message': '输入文本成功 [输入框]: set split-screen apps', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 730296, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=192914), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_input_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 247, 'message': '✅ 文本输入成功', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 731295, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=193920), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_send_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 321, 'message': '发送命令', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 732301, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=377780), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [发送按钮]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 10, 916161, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=485931), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 62, 'message': '等待元素出现 [发送按钮], 超时时间: 5秒', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 11, 24312, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=509971), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 65, 'message': '元素列表 [True]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 11, 48352, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=510988), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 67, 'message': '元素已出现 [发送按钮]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 11, 49369, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=722201), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_popup_tool', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 164, 'message': '✅ 弹窗处理工具初始化成功', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 11, 260582, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=721048), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'click', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 231, 'message': '点击元素成功 [发送按钮]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 11, 259429, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=721623), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_send_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 327, 'message': '✅ 点击发送按钮成功', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 11, 260004, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=721623), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': 'execute_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 53, 'message': '✅ 文本命令执行完成', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 11, 260004, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=721623), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_execute_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 870, 'message': '✅ 成功执行命令: set split-screen apps', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 11, 260004, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=722201), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_handle_popup_after_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 178, 'message': 'handle_popup_after_command:处理弹窗', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 11, 260582, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15349, microseconds=722201), 'exception': None, 'extra': {}, 'file': (name='popup_tool.py', path='D:\\aigc\\app_test\\core\\popup_tool.py'), 'function': 'detect_and_close_popup_once', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 738, 'message': '执行单次弹窗检测和关闭', 'module': 'popup_tool', 'name': 'core.popup_tool', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 11, 260582, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15350, microseconds=681591), 'exception': None, 'extra': {}, 'file': (name='popup_tool.py', path='D:\\aigc\\app_test\\core\\popup_tool.py'), 'function': 'detect_and_close_popup_once', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 742, 'message': '未检测到弹窗，无需处理', 'module': 'popup_tool', 'name': 'core.popup_tool', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 12, 219972, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
--- Logging error in Loguru Handler #4 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
Record was: {'elapsed': datetime.timedelta(seconds=15350, microseconds=681591), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_handle_popup_after_command', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 193, 'message': 'ℹ️ 未检测到弹窗或无需处理，命令: set split-screen apps', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 12, 219972, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
Traceback (most recent call last):
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15350, microseconds=681591), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_response_timeout', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1299, 'message': '📝 使用默认等待时间: 8秒', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 12, 219972, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15350, microseconds=682591), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'wait_for_response', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 35, 'message': '等待AI响应，超时时间: 8秒', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 12, 220972, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15350, microseconds=857785), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'12:44 am\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', \'Haliburton Updates Achilles Rehab\', \'Switch voices\', \'Meta & Google Ink $10B Cloud Deal\']...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 12, 396166, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15351, microseconds=53536), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'12:44 am\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', \'Haliburton Updates Achilles Rehab\', \'Switch voices\', \'Meta & Google Ink $10B Cloud Deal\']...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 12, 591917, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15351, microseconds=198904), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_detect_command_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 621, 'message': '未识别的命令类型: set split-screen apps', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 12, 737285, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15351, microseconds=197895), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'wait_for_response', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 60, 'message': '✅ 通过TTS按钮检测到响应', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 12, 736276, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15351, microseconds=198904), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_final_status_with_page_info', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 432, 'message': '等待状态变化: 3.0秒', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 12, 737285, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=440368), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_final_status_with_page_info', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 440, 'message': '状态检查时当前应用包名: com.transsion.aivoiceassistant', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 15, 978749, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=441397), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 558, 'message': '状态检查完成，现在获取响应文本', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 15, 979778, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=441397), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 562, 'message': '第1次尝试确保在Ella页面以获取响应', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 15, 979778, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=442367), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 769, 'message': '确保在对话页面...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 15, 980748, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=442367), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 15, 980748, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=709987), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 248368, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
--- Logging error in Loguru Handler #4 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=709987), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': '检查对话页面指示器...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 248368, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=709987), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 248368, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=709987), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 248368, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=749858), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 288239, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=793465), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 812, 'message': '✅ 找到主输入框', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 331846, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=793465), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 780, 'message': '✅ 已在对话页面', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 331846, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=793465), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 566, 'message': '✅ 已确认在Ella对话页面，可以获取响应', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 331846, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15354, microseconds=794800), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 333181, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=27460), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 565841, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=28448), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 566829, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=28448), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 566829, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=29448), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_ensure_on_ella_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1335, 'message': '检查是否在Ella页面...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 567829, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=29448), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 567829, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=246499), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 784880, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=247683), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从asr_txt节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 786064, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=247003), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 785384, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=247003), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 785384, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=247003), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_ensure_on_ella_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1344, 'message': '✅ 当前在Ella页面', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 785384, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=247683), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'get_response_all_text', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 140, 'message': '获取AI响应文本', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 786064, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=378214), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 474, 'message': '✅ 从asr_txt成功获取响应: set split-screen apps', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 916595, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
--- Logging error in Loguru Handler #4 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=377212), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从asr_txt节点获取到原始文本: "set split-screen apps"', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 915593, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=377212), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'set split-screen apps'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 915593, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=377212), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: set split-screen apps', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 915593, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=378214), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从robot_text节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 16, 916595, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=595323), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 474, 'message': "✅ 从robot_text成功获取响应: Sorry, I couldn't locate the setting option(s) for split-screen apps.", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 17, 133704, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=595323), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从robot_text节点获取到原始文本: "Sorry, I couldn\'t locate the setting option(s) for split-screen apps."', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 17, 133704, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=595323), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'Sorry, I couldn't locate the setting option(s) for split-screen apps.'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 17, 133704, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=595323), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': "✅ 文本长度和内容合理，认为是有效响应: Sorry, I couldn't locate the setting option(s) for split-screen apps.", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 17, 133704, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=595323), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 17, 133704, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15355, microseconds=675409), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 17, 213790, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15356, microseconds=176051), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 17, 714432, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15356, microseconds=203624), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 17, 742005, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15356, microseconds=704940), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 18, 243321, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15356, microseconds=740570), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 18, 278951, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15356, microseconds=740570), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'function_name节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 18, 278951, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
Traceback (most recent call last):
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15356, microseconds=741570), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 18, 279951, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15356, microseconds=759114), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 18, 297495, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15357, microseconds=260417), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 18, 798798, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15357, microseconds=285033), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 18, 823414, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15357, microseconds=786080), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 19, 324461, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15357, microseconds=817872), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'function_control节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 19, 356253, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
--- Logging error in Loguru Handler #4 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
Record was: {'elapsed': datetime.timedelta(seconds=15357, microseconds=817872), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 19, 356253, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15357, microseconds=817872), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 19, 356253, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15357, microseconds=841901), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 19, 380282, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15358, microseconds=342659), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 19, 881040, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15358, microseconds=370764), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 19, 909145, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15358, microseconds=871473), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 20, 409854, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15358, microseconds=911236), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 20, 449617, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15358, microseconds=911236), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'tv_card_chat_gpt节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 20, 449617, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15358, microseconds=912224), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 20, 450605, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15358, microseconds=934886), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 20, 473267, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15359, microseconds=435383), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 20, 973764, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15359, microseconds=464750), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 21, 3131, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15359, microseconds=965777), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 21, 504158, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15359, microseconds=993209), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'tv_top节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 21, 531590, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15359, microseconds=993209), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 21, 531590, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15359, microseconds=994209), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 21, 532590, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15360, microseconds=22240), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 21, 560621, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15360, microseconds=523623), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 62004, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15360, microseconds=549889), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 88270, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=50912), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 589293, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=86515), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 624896, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=86515), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'tv_banner节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 624896, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
Traceback (most recent call last):
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=87518), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_text节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 625899, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=211586), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 474, 'message': '✅ 从tv_text成功获取响应: I can answer your questions, summarize content, and provide creative inspiration.', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 749967, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=210585), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从tv_text节点获取到原始文本: "I can answer your questions, summarize content, and provide creative inspiration."', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 748966, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=210585), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'I can answer your questions, summarize content, and provide creative inspiration.'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 748966, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=210585), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: I can answer your questions, summarize content, and provide creative inspiration.', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 748966, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=211586), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 749967, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=248663), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 22, 787044, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=749452), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 23, 287833, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15361, microseconds=786548), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 23, 324929, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15362, microseconds=287686), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 23, 826067, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15362, microseconds=324346), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'alarm_time_tv节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 23, 862727, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15362, microseconds=323353), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 23, 861734, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15362, microseconds=324346), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 23, 862727, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15362, microseconds=363798), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 23, 902179, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15362, microseconds=864894), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 24, 403275, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15362, microseconds=898668), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 24, 437049, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15363, microseconds=399742), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 24, 938123, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15363, microseconds=441033), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 24, 979414, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15363, microseconds=441033), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'alarm_day_tv节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 24, 979414, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15363, microseconds=441033), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 24, 979414, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15363, microseconds=506364), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 25, 44745, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=7531), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 25, 545912, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=62648), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 25, 601029, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=563369), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 26, 101750, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=597299), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 26, 135680, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=597299), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 530, 'message': 'alarm_switch节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 26, 135680, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
--- End of logging error ---
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=598419), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 596, 'message': '最终获取的AI响应: \'[\'set split-screen apps\', "Sorry, I couldn\'t locate the setting option(s) for split-screen apps.", \'\', \'\', \'\', \'\', \'\', \'I can answer your questions, summarize content, and provide creative inspiration.\', \'\', \'\', \'\']\'', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 26, 136800, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=793620), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1057, 'message': "⚠️ 响应未包含期望内容: 'Done'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 26, 332001, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=793620), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1064, 'message': '❌ 部分期望内容未找到 (0/1)', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 26, 332001, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=793620), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1065, 'message': "缺失内容: ['Done']", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 26, 332001, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=794637), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1066, 'message': "搜索文本: 'set split-screen apps Sorry, I couldn't locate the setting option(s) for split-screen apps. I can answer your questions, summarize content, and provide creative inspiration.'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 26, 333018, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=794637), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1068, 'message': "响应未包含期望内容: '['Done']'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 26, 333018, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=15364, microseconds=966492), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'stop_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 388, 'message': '停止Ella应用', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 44, 26, 504873, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
