--- Logging error in <PERSON><PERSON><PERSON> Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12110, microseconds=899333), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'setup_batch_test_screen', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 64, 'message': '📱 批量测试屏幕设置已完成，跳过重复设置', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 437714, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in <PERSON><PERSON>ru Handler #2 ---
Traceback (most recent call last):
Record was: {'elapsed': datetime.timedelta(seconds=12110, microseconds=903329), 'exception': None, 'extra': {}, 'file': (name='base_page.py', path='D:\\aigc\\app_test\\core\\base_page.py'), 'function': '__init__', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 38, 'message': '初始化页面: ella - dialogue_page', 'module': 'base_page', 'name': 'core.base_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 441710, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
Traceback (most recent call last):
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12110, microseconds=982698), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': '_init_detectors', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 469, 'message': '✅ 使用绝对路径导入成功 - 所有检测器', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 521079, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12110, microseconds=983283), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 299, 'message': '🧹 开始清除手机上所有运行中的应用进程...', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 521664, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12110, microseconds=983283), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': '_init_detectors', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 586, 'message': '✅ 成功初始化 83 个检测器', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 521664, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12110, microseconds=983283), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1825, 'message': '🧹 开始清除手机上所有运行中的应用进程...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 521664, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12110, microseconds=984790), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_load_process_cleanup_config', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1758, 'message': '✅ 已加载进程清理配置，共 3 项', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 523171, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12110, microseconds=984790), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1841, 'message': '⚡ 优先使用命令直接清理...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 523171, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12110, microseconds=984790), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_command_clear_all_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1872, 'message': '⚡ 执行命令直接清理...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 523171, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12110, microseconds=985797), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_stop_running_apps_by_list', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1901, 'message': '📋 获取运行应用列表并逐个停止...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 524178, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12111, microseconds=295892), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1975, 'message': '🔧 执行系统级清理命令...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 834273, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12111, microseconds=448405), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: am kill-all', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 12, 986786, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12111, microseconds=553352), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: pm trim-caches 1000M', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 13, 91733, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12111, microseconds=786387), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: rm -rf /data/local/tmp/*', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 13, 324768, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12111, microseconds=786897), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2008, 'message': '📱 按类别清理应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 13, 325278, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12111, microseconds=897713), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Facebook', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 13, 436094, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12112, microseconds=967), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Facebook Lite', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 13, 539348, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12112, microseconds=110711), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: WhatsApp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 13, 649092, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12112, microseconds=216838), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Instagram', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 13, 755219, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12112, microseconds=328543), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Twitter', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 13, 866924, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12112, microseconds=451838), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: 微信', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 13, 990219, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12112, microseconds=553848), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: QQ', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 14, 92229, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12112, microseconds=658570), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: 微博', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 14, 196951, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12112, microseconds=772756), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: Spotify', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 14, 311137, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12112, microseconds=904641), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: Netflix', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 14, 443022, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12113, microseconds=17755), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: 抖音', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 14, 556136, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12113, microseconds=110849), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: 快手', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 14, 649230, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12113, microseconds=220718), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理game应用: 王者荣耀', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 14, 759099, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12113, microseconds=320701), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理game应用: 和平精英', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 14, 859082, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12113, microseconds=460500), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理browser应用: Chrome浏览器', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 14, 998881, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12113, microseconds=586772), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理browser应用: UC浏览器', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 15, 125153, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
--- Logging error in Loguru Handler #2 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
Record was: {'elapsed': datetime.timedelta(seconds=12113, microseconds=587770), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1847, 'message': '💪 强制停止顽固应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 15, 126151, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12113, microseconds=587770), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_command_clear_all_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1888, 'message': '✅ 命令清理完成，清理了 22 个应用', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 15, 126151, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12113, microseconds=588770), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2054, 'message': '💪 强制停止顽固应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 15, 127151, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12113, microseconds=704061), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.google.android.apps.maps', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 15, 242442, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12114, microseconds=146096), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.google.android.apps.maps', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 15, 684477, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12114, microseconds=269682), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.google.android.gms', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 15, 808063, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12114, microseconds=687968), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.google.android.gms', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 16, 226349, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12114, microseconds=817138), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.android.chrome', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 16, 355519, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12115, microseconds=209311), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.android.chrome', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 16, 747692, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12115, microseconds=314586), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.facebook.katana', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 16, 852967, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12115, microseconds=726803), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.facebook.katana', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 17, 265184, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12115, microseconds=825449), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.whatsapp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 17, 363830, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12116, microseconds=243372), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.whatsapp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 17, 781753, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12116, microseconds=358113), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.tencent.mm', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 17, 896494, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12116, microseconds=791919), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.tencent.mm', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 18, 330300, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12116, microseconds=900521), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.netflix.mediaclient', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 18, 438902, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12117, microseconds=297641), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.netflix.mediaclient', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 18, 836022, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12117, microseconds=409385), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.spotify.music', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 18, 947766, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12117, microseconds=813863), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.spotify.music', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 19, 352244, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12117, microseconds=921243), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2111, 'message': '🧹 执行系统内存清理', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 19, 459624, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12118, microseconds=23339), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1857, 'message': '🎉 应用进程清理完成，共清理 30 个应用', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 19, 561720, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12118, microseconds=23339), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2118, 'message': '🗑️ 执行垃圾回收', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 19, 561720, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12120, microseconds=24668), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 301, 'message': '✅ 应用进程清理完成', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 21, 563049, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12120, microseconds=25220), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'start_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 214, 'message': '启动Ella应用', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 21, 563601, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12120, microseconds=93164), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'start_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 222, 'message': '尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 21, 631545, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=360442), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_app_started', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 280, 'message': '✅ 应用已在前台: com.transsion.aivoiceassistant', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 24, 898823, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=361436), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'start_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 227, 'message': '✅ Ella应用启动成功（指定Activity）', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 24, 899817, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=362570), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'wait_for_page_load', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 301, 'message': '等待Ella页面加载完成 (超时: 15秒)', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 24, 900951, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=592092), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'wait_for_page_load', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 311, 'message': '✅ 确认当前在Ella应用中', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 130473, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=643411), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 181792, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=660485), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'wait_for_page_load', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 320, 'message': '✅ 主输入框已出现，页面加载完成', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 198866, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=662492), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_load_status_check_config', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 287, 'message': '✅ 已加载状态检查配置，共 38 项', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 200873, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
Traceback (most recent call last):
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=663936), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_detect_command_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 621, 'message': '未识别的命令类型: how to set screenshots', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 202317, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=661455), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'ella_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 333, 'message': '✅ Ella应用启动成功', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 199836, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=663936), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'execute_command_and_verify', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 366, 'message': '初始状态None- 使用命令how to set screenshots，状态: ', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 202317, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=664953), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 769, 'message': '确保在对话页面...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 203334, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=665947), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 204328, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=816699), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 355080, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=818742), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': '检查对话页面指示器...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 357123, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
--- End of logging error ---
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=817742), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 356123, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=817742), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 356123, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=854850), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 393231, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=910616), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 780, 'message': '✅ 已在对话页面', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 448997, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=910616), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 812, 'message': '✅ 找到主输入框', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 448997, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=911617), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_ensure_input_box_ready', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 130, 'message': '确保输入框就绪...', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 449998, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=943556), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 481937, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=980549), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_check_known_input_elements', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 160, 'message': '✅ 找到主输入框', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 518930, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=981587), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': 'execute_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 34, 'message': '执行文本命令: how to set screenshots', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 519968, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12123, microseconds=983603), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_ensure_input_box_ready', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 130, 'message': '确保输入框就绪...', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 521984, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=24655), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 563036, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=51675), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_check_known_input_elements', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 160, 'message': '✅ 找到主输入框', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 590056, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=52693), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_input_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 230, 'message': '输入文本命令: how to set screenshots', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 591074, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=85348), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 623729, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=124059), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 62, 'message': '等待元素出现 [输入框], 超时时间: 5秒', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 662440, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=162965), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 65, 'message': '元素列表 [True]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 701346, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=163956), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 67, 'message': '元素已出现 [输入框]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 702337, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=279842), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'clear_text', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 325, 'message': '清空文本成功 [输入框]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 818223, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=323033), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 861414, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=352265), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 62, 'message': '等待元素出现 [输入框], 超时时间: 5秒', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 890646, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=406442), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 65, 'message': '元素列表 [True]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 944823, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=406442), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 67, 'message': '元素已出现 [输入框]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 25, 944823, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=595990), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'send_keys', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 302, 'message': '输入文本成功 [输入框]: how to set screenshots', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 134371, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=595990), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_input_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 247, 'message': '✅ 文本输入成功', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 134371, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=597146), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_send_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 321, 'message': '发送命令', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 135527, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=666661), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [发送按钮]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 205042, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=698364), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 62, 'message': '等待元素出现 [发送按钮], 超时时间: 5秒', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 236745, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=822789), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 65, 'message': '元素列表 [True]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 361170, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12124, microseconds=823790), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 67, 'message': '元素已出现 [发送按钮]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 362171, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12125, microseconds=54086), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'click', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 231, 'message': '点击元素成功 [发送按钮]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 592467, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Traceback (most recent call last):
Record was: {'elapsed': datetime.timedelta(seconds=12125, microseconds=58486), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_popup_tool', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 164, 'message': '✅ 弹窗处理工具初始化成功', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 596867, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12125, microseconds=54086), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_send_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 327, 'message': '✅ 点击发送按钮成功', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 592467, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12125, microseconds=55092), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': 'execute_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 53, 'message': '✅ 文本命令执行完成', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 593473, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12125, microseconds=56378), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_execute_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 870, 'message': '✅ 成功执行命令: how to set screenshots', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 594759, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12125, microseconds=57442), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_handle_popup_after_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 178, 'message': 'handle_popup_after_command:处理弹窗', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 595823, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12125, microseconds=59485), 'exception': None, 'extra': {}, 'file': (name='popup_tool.py', path='D:\\aigc\\app_test\\core\\popup_tool.py'), 'function': 'detect_and_close_popup_once', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 738, 'message': '执行单次弹窗检测和关闭', 'module': 'popup_tool', 'name': 'core.popup_tool', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 26, 597866, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12126, microseconds=813), 'exception': None, 'extra': {}, 'file': (name='popup_tool.py', path='D:\\aigc\\app_test\\core\\popup_tool.py'), 'function': 'detect_and_close_popup_once', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 742, 'message': '未检测到弹窗，无需处理', 'module': 'popup_tool', 'name': 'core.popup_tool', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 27, 539194, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12126, microseconds=1813), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_handle_popup_after_command', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 193, 'message': 'ℹ️ 未检测到弹窗或无需处理，命令: how to set screenshots', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 27, 540194, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
--- Logging error in Loguru Handler #2 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Record was: {'elapsed': datetime.timedelta(seconds=12126, microseconds=1813), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_response_timeout', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1295, 'message': '💬 检测到简单对话命令，使用短等待时间: 6秒', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 27, 540194, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12126, microseconds=3748), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'wait_for_response', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 35, 'message': '等待AI响应，超时时间: 6秒', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 27, 542129, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12126, microseconds=204939), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'11:50 pm\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', \'What is Ask About Screen?\', "Bissouma\'s West Ham Loan Move", "Trump\'s National Guard Deployment Legality"]...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 27, 743320, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12126, microseconds=372278), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'11:50 pm\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', \'What is Ask About Screen?\', "Bissouma\'s West Ham Loan Move", "Trump\'s National Guard Deployment Legality"]...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 27, 910659, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12126, microseconds=509888), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_detect_command_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 621, 'message': '未识别的命令类型: how to set screenshots', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 28, 48269, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12126, microseconds=508886), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'wait_for_response', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 60, 'message': '✅ 通过TTS按钮检测到响应', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 28, 47267, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12126, microseconds=509888), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_final_status_with_page_info', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 432, 'message': '等待状态变化: 3.0秒', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 28, 48269, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=675781), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_final_status_with_page_info', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 440, 'message': '状态检查时当前应用包名: com.transsion.aivoiceassistant', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 214162, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=676288), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 558, 'message': '状态检查完成，现在获取响应文本', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 214669, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=676288), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 562, 'message': '第1次尝试确保在Ella页面以获取响应', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 214669, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=677301), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 769, 'message': '确保在对话页面...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 215682, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=677301), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 215682, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=883505), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 421886, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=884046), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': '检查对话页面指示器...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 422427, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=884046), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 422427, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=884046), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 422427, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=951715), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 490096, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=978197), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 780, 'message': '✅ 已在对话页面', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 516578, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=977195), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 812, 'message': '✅ 找到主输入框', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 515576, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=978197), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 566, 'message': '✅ 已确认在Ella对话页面，可以获取响应', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 516578, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12129, microseconds=980195), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 518576, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=200058), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 738439, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=200058), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 738439, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=201507), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 739888, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=201507), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_ensure_on_ella_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1335, 'message': '检查是否在Ella页面...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 739888, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=201507), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 739888, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=406628), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 945009, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=406628), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从asr_txt节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 945009, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=406628), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 945009, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=406628), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 945009, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=406628), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_ensure_on_ella_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1344, 'message': '✅ 当前在Ella页面', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 945009, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=406628), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'get_response_all_text', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 140, 'message': '获取AI响应文本', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 31, 945009, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=504508), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从asr_txt节点获取到原始文本: "how to set screenshots"', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 42889, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Traceback (most recent call last):
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=505477), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 474, 'message': '✅ 从asr_txt成功获取响应: how to set screenshots', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 43858, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=505477), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'how to set screenshots'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 43858, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=505477), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: how to set screenshots', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 43858, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=506520), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从robot_text节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 44901, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=740035), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从robot_text节点获取到原始文本: "Sorry, I couldn\'t locate the setting option(s) for screenshots."', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 278416, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=742269), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 474, 'message': "✅ 从robot_text成功获取响应: Sorry, I couldn't locate the setting option(s) for screenshots.", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 280650, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=740035), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'Sorry, I couldn't locate the setting option(s) for screenshots.'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 278416, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=741039), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': "✅ 文本长度和内容合理，认为是有效响应: Sorry, I couldn't locate the setting option(s) for screenshots.", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 279420, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=743286), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 281667, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12130, microseconds=802421), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 340802, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12131, microseconds=303945), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 842326, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12131, microseconds=342891), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 32, 881272, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12131, microseconds=843640), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 33, 382021, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12131, microseconds=870827), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'function_name节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 33, 409208, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12131, microseconds=869832), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 33, 408213, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12131, microseconds=870827), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 33, 409208, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12131, microseconds=896910), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 33, 435291, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12132, microseconds=397829), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 33, 936210, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12132, microseconds=434224), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 33, 972605, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12132, microseconds=935103), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 34, 473484, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12132, microseconds=986460), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'function_control节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 34, 524841, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12132, microseconds=986460), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 34, 524841, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12132, microseconds=987462), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 34, 525843, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12133, microseconds=17251), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 34, 555632, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12133, microseconds=517922), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 35, 56303, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12133, microseconds=546881), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 35, 85262, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12134, microseconds=47883), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 35, 586264, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12134, microseconds=93953), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_card_chat_gpt节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 35, 632334, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12134, microseconds=93953), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'tv_card_chat_gpt节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 35, 632334, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12134, microseconds=93953), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 35, 632334, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12134, microseconds=125558), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 35, 663939, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12134, microseconds=626593), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 36, 164974, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12134, microseconds=662570), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 36, 200951, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12135, microseconds=164012), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 36, 702393, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12135, microseconds=210019), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_top节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 36, 748400, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12135, microseconds=211020), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'tv_top节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 36, 749401, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12135, microseconds=211020), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 36, 749401, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12135, microseconds=240462), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 36, 778843, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12135, microseconds=741502), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 279883, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12135, microseconds=777589), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 315970, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=279612), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 817993, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=309648), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 848029, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
--- Logging error in Loguru Handler #2 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=310669), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'tv_banner节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 849050, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=311681), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_text节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 850062, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=432633), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从tv_text节点获取到原始文本: "I can answer your questions, summarize content, and provide creative inspiration."', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 971014, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=433204), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 474, 'message': '✅ 从tv_text成功获取响应: I can answer your questions, summarize content, and provide creative inspiration.', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 971585, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=433204), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'I can answer your questions, summarize content, and provide creative inspiration.'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 971585, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=433204), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: I can answer your questions, summarize content, and provide creative inspiration.', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 971585, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=434249), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 37, 972630, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=474222), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 38, 12603, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12136, microseconds=976042), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 38, 514423, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12137, microseconds=13049), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 38, 551430, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12137, microseconds=514311), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 39, 52692, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12137, microseconds=544231), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 39, 82612, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12137, microseconds=544231), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'alarm_time_tv节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 39, 82612, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12137, microseconds=545233), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 39, 83614, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12137, microseconds=587264), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 39, 125645, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12138, microseconds=88659), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 39, 627040, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12138, microseconds=122862), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 39, 661243, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12138, microseconds=624010), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 40, 162391, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12138, microseconds=655029), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 40, 193410, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12138, microseconds=656021), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'alarm_day_tv节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 40, 194402, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12138, microseconds=656021), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 40, 194402, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12138, microseconds=683056), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 40, 221437, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=183408), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 40, 721789, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=219354), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 40, 757735, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=720850), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 41, 259231, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=785412), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 41, 323793, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=786418), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 530, 'message': 'alarm_switch节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 41, 324799, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=786418), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 596, 'message': '最终获取的AI响应: \'[\'how to set screenshots\', "Sorry, I couldn\'t locate the setting option(s) for screenshots.", \'\', \'\', \'\', \'\', \'\', \'I can answer your questions, summarize content, and provide creative inspiration.\', \'\', \'\', \'\']\'', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 41, 324799, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=959994), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1057, 'message': "⚠️ 响应未包含期望内容: 'Done'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 41, 498375, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=959994), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1064, 'message': '❌ 部分期望内容未找到 (0/1)', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 41, 498375, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=959994), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1065, 'message': "缺失内容: ['Done']", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 41, 498375, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=959994), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1066, 'message': "搜索文本: 'how to set screenshots Sorry, I couldn't locate the setting option(s) for screenshots. I can answer your questions, summarize content, and provide creative inspiration.'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 41, 498375, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12139, microseconds=960993), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1068, 'message': "响应未包含期望内容: '['Done']'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 41, 499374, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=12140, microseconds=167408), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'stop_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 388, 'message': '停止Ella应用', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 41, 705789, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=12141, microseconds=473985), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_verify_app_stopped', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 426, 'message': '当前前台应用: com.android.vending，目标应用已不在前台', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 26, 23, 50, 43, 12366, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
