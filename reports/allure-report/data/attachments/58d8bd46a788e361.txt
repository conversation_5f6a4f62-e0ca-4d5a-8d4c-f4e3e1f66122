2025-08-27 00:22:13 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-08-27 00:22:13 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:299 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-27 00:22:13 | INFO | tools.adb_process_monitor:clear_all_running_processes:1825 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-27 00:22:13 | INFO | tools.adb_process_monitor:clear_all_running_processes:1841 | ⚡ 优先使用命令直接清理...
2025-08-27 00:22:16 | INFO | tools.adb_process_monitor:clear_all_running_processes:1847 | 💪 强制停止顽固应用...
2025-08-27 00:22:20 | INFO | tools.adb_process_monitor:clear_all_running_processes:1857 | 🎉 应用进程清理完成，共清理 30 个应用
2025-08-27 00:22:22 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:301 | ✅ 应用进程清理完成
2025-08-27 00:22:22 | INFO | pages.apps.ella.dialogue_page:start_app:214 | 启动Ella应用
2025-08-27 00:22:22 | INFO | pages.apps.ella.dialogue_page:start_app:222 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-27 00:22:25 | INFO | pages.apps.ella.dialogue_page:_check_app_started:280 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-08-27 00:22:25 | INFO | pages.apps.ella.dialogue_page:start_app:227 | ✅ Ella应用启动成功（指定Activity）
2025-08-27 00:22:25 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:301 | 等待Ella页面加载完成 (超时: 15秒)
2025-08-27 00:22:26 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:311 | ✅ 确认当前在Ella应用中
2025-08-27 00:22:26 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:320 | ✅ 主输入框已出现，页面加载完成
2025-08-27 00:22:26 | INFO | testcases.test_ella.base_ella_test:ella_app:333 | ✅ Ella应用启动成功
2025-08-27 00:22:26 | INFO | pages.base.app_detector:check_app_opened:156 | 检查headset_control应用状态
2025-08-27 00:22:26 | INFO | pages.base.app_detector:check_app_opened:170 | 未检测到headset_control应用
2025-08-27 00:22:26 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:366 | 初始状态False- 使用命令running on the grass，状态: 
2025-08-27 00:22:26 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:769 | 确保在对话页面...
2025-08-27 00:22:26 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-27 00:22:27 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-27 00:22:27 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-27 00:22:27 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-27 00:22:27 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:780 | ✅ 已在对话页面
2025-08-27 00:22:27 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-27 00:22:27 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-27 00:22:27 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: running on the grass
2025-08-27 00:22:27 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-27 00:22:27 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-27 00:22:27 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: running on the grass
2025-08-27 00:22:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-08-27 00:22:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-27 00:22:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-27 00:22:27 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-08-27 00:22:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-08-27 00:22:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-27 00:22:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-27 00:22:27 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: running on the grass
2025-08-27 00:22:27 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-08-27 00:22:27 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-08-27 00:22:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-08-27 00:22:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-27 00:22:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-08-27 00:22:28 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-08-27 00:22:28 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-08-27 00:22:28 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-08-27 00:22:28 | INFO | testcases.test_ella.base_ella_test:_execute_command:870 | ✅ 成功执行命令: running on the grass
2025-08-27 00:22:28 | INFO | testcases.test_ella.base_ella_test:_handle_popup_after_command:178 | handle_popup_after_command:处理弹窗
2025-08-27 00:22:28 | INFO | core.popup_tool:detect_and_close_popup_once:738 | 执行单次弹窗检测和关闭
2025-08-27 00:22:29 | INFO | core.popup_tool:detect_and_close_popup_once:742 | 未检测到弹窗，无需处理
2025-08-27 00:22:29 | INFO | testcases.test_ella.base_ella_test:_get_response_timeout:1299 | 📝 使用默认等待时间: 8秒
2025-08-27 00:22:29 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:35 | 等待AI响应，超时时间: 8秒
2025-08-27 00:22:29 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:60 | ✅ 通过TTS按钮检测到响应
2025-08-27 00:22:33 | INFO | testcases.test_ella.base_ella_test:_get_final_status_with_page_info:440 | 状态检查时当前应用包名: com.transsion.aivoiceassistant
2025-08-27 00:22:33 | INFO | pages.base.app_detector:check_app_opened:156 | 检查headset_control应用状态
2025-08-27 00:22:33 | INFO | pages.base.app_detector:check_app_opened:170 | 未检测到headset_control应用
2025-08-27 00:22:33 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:558 | 状态检查完成，现在获取响应文本
2025-08-27 00:22:33 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:562 | 第1次尝试确保在Ella页面以获取响应
2025-08-27 00:22:33 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:769 | 确保在对话页面...
2025-08-27 00:22:33 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-27 00:22:34 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:780 | ✅ 已在对话页面
2025-08-27 00:22:34 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:566 | ✅ 已确认在Ella对话页面，可以获取响应
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-27 00:22:34 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:1335 | 检查是否在Ella页面...
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-27 00:22:34 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-27 00:22:34 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:1344 | ✅ 当前在Ella页面
2025-08-27 00:22:34 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:140 | 获取AI响应文本
2025-08-27 00:22:34 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从asr_txt成功获取响应: running on the grass
2025-08-27 00:22:35 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | robot_text节点不存在，已达到最大重试次数
2025-08-27 00:22:37 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | function_name节点不存在，已达到最大重试次数
2025-08-27 00:22:38 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | function_control节点不存在，已达到最大重试次数
2025-08-27 00:22:38 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从tv_card_chat_gpt成功获取响应: That sounds like a lovely image! Running on the grass is great. Are you thinking about:

A specific type of grass? (e.g., soft, green, freshly cut)
Who is running? (A person,
2025-08-27 00:22:38 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从tv_top成功获取响应: Generated by AI, for reference only
2025-08-27 00:22:40 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_banner节点不存在，已达到最大重试次数
2025-08-27 00:22:40 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从tv_text成功获取响应: I can answer your questions, summarize content, and provide creative inspiration.
2025-08-27 00:22:41 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | alarm_time_tv节点不存在，已达到最大重试次数
2025-08-27 00:22:42 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | alarm_day_tv节点不存在，已达到最大重试次数
2025-08-27 00:22:44 | WARNING | pages.apps.ella.ella_response_handler:_get_element_checked_with_retry:530 | alarm_switch节点不存在，已达到最大重试次数
2025-08-27 00:22:44 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:168 | 尝试获取其他有效的响应文本
2025-08-27 00:22:44 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:1049 | 从TextView元素获取响应
2025-08-27 00:22:44 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:1054 | 从TextView获取响应: Dialogue
2025-08-27 00:22:44 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:180 | ✅ 获取到响应文本: Dialogue
2025-08-27 00:22:44 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:1065 | 查找RecyclerView中的最新消息
2025-08-27 00:22:44 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:897 | 从dump正则提取文本: Dialogue Explore Swipe down to view earlier chats running on the grass That sounds like a lovely image! Running on the grass is great. Are you thinking about:&#10;&#10;A specific type of grass? (e.g., soft, green, freshly cut)&#10;Who is running? (A person, a dog, etc.)&#10;Where are they running? (A park, a field, your backyard)&#10;What are they feeling? (Happy, free, energetic)&#10;&#10;Tell me more, and let's explore this image! Generated by AI, for reference only What is the weather like? Is it a sunny day there? I like running on grass. DeepSeek-R1 Feel free to ask me any questions… 12:22
2025-08-27 00:22:44 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:180 | ✅ 获取到响应文本: Dialogue Explore Swipe down to view earlier chats running on the grass That sounds like a lovely image! Running on the grass is great. Are you thinking about:&#10;&#10;A specific type of grass? (e.g., soft, green, freshly cut)&#10;Who is running? (A person, a dog, etc.)&#10;Where are they running? (A park, a field, your backyard)&#10;What are they feeling? (Happy, free, energetic)&#10;&#10;Tell me more, and let's explore this image! Generated by AI, for reference only What is the weather like? Is it a sunny day there? I like running on grass. DeepSeek-R1 Feel free to ask me any questions… 12:22
2025-08-27 00:22:44 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:185 | 未获取到有效的响应文本
2025-08-27 00:22:44 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:596 | 最终获取的AI响应: '['running on the grass', '', '', '', 'That sounds like a lovely image! Running on the grass is great. Are you thinking about:\n\nA specific type of grass? (e.g., soft, green, freshly cut)\nWho is running? (A person,', 'Generated by AI, for reference only', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', "Dialogue Explore Swipe down to view earlier chats running on the grass That sounds like a lovely image! Running on the grass is great. Are you thinking about:&#10;&#10;A specific type of grass? (e.g., soft, green, freshly cut)&#10;Who is running? (A person, a dog, etc.)&#10;Where are they running? (A park, a field, your backyard)&#10;What are they feeling? (Happy, free, energetic)&#10;&#10;Tell me more, and let's explore this image! Generated by AI, for reference only What is the weather like? Is it a sunny day there? I like running on grass. DeepSeek-R1 Feel free to ask me any questions… 12:22"]'
2025-08-27 00:22:44 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:973 | ✅ 状态验证通过: False -> False
2025-08-27 00:22:44 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaOpenPlayPoliticalNews\test_completed.png
2025-08-27 00:22:44 | INFO | testcases.test_ella.base_ella_test:simple_command_test:1374 | 🎉 running on the grass 测试完成
2025-08-27 00:22:44 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1090 | verify_expected_in_response_advanced 响应类型: <class 'list'>, 搜索模式: combined, 匹配模式: 任意匹配
2025-08-27 00:22:44 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1158 | ⚠️ 未找到期望内容: 'The following images are generated for you.'
2025-08-27 00:22:44 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1158 | ⚠️ 未找到期望内容: 'Do you enjoy being outdoors?'
2025-08-27 00:22:44 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1158 | ⚠️ 未找到期望内容: 'enjoy running'
2025-08-27 00:22:44 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1158 | ⚠️ 未找到期望内容: 'outdoors'
2025-08-27 00:22:44 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1158 | ⚠️ 未找到期望内容: 'Do you have a favorite park or location where you like to go running on the grass?'
2025-08-27 00:22:44 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1133 | ✅ [合并模式] 找到期望内容: 'Generated by AI, for reference only'
2025-08-27 00:22:44 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1154 | 🎉 [任意匹配模式] 找到期望内容，验证通过: 'Generated by AI, for reference only'
2025-08-27 00:22:44 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaOpenPlayPoliticalNews\test_completed.png
2025-08-27 00:22:44 | INFO | pages.apps.ella.dialogue_page:stop_app:388 | 停止Ella应用
2025-08-27 00:22:46 | INFO | pages.apps.ella.dialogue_page:stop_app:399 | ✅ Ella应用已成功停止
