--- Logging error in <PERSON><PERSON><PERSON> Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13054, microseconds=982480), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'setup_batch_test_screen', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 64, 'message': '📱 批量测试屏幕设置已完成，跳过重复设置', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 520861, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13054, microseconds=984873), 'exception': None, 'extra': {}, 'file': (name='base_page.py', path='D:\\aigc\\app_test\\core\\base_page.py'), 'function': '__init__', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 38, 'message': '初始化页面: ella - dialogue_page', 'module': 'base_page', 'name': 'core.base_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 523254, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=88143), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': '_init_detectors', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 469, 'message': '✅ 使用绝对路径导入成功 - 所有检测器', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 626524, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=90142), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 299, 'message': '🧹 开始清除手机上所有运行中的应用进程...', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 628523, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=89147), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': '_init_detectors', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 586, 'message': '✅ 成功初始化 83 个检测器', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 627528, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=91142), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1825, 'message': '🧹 开始清除手机上所有运行中的应用进程...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 629523, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=93136), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_load_process_cleanup_config', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1758, 'message': '✅ 已加载进程清理配置，共 3 项', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 631517, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=93136), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1841, 'message': '⚡ 优先使用命令直接清理...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 631517, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=94124), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_command_clear_all_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1872, 'message': '⚡ 执行命令直接清理...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 632505, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=94124), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_stop_running_apps_by_list', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1901, 'message': '📋 获取运行应用列表并逐个停止...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 632505, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=383621), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1975, 'message': '🔧 执行系统级清理命令...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 56, 922002, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=554016), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: am kill-all', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 57, 92397, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=693687), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: pm trim-caches 1000M', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 57, 232068, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=940129), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_system_level_cleanup', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1993, 'message': '✅ 执行系统命令: rm -rf /data/local/tmp/*', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 57, 478510, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13055, microseconds=941128), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2008, 'message': '📱 按类别清理应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 57, 479509, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13056, microseconds=66971), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Facebook', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 57, 605352, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13056, microseconds=210680), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Facebook Lite', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 57, 749061, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13056, microseconds=338567), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: WhatsApp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 57, 876948, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13056, microseconds=464529), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Instagram', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 58, 2910, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13056, microseconds=584233), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: Twitter', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 58, 122614, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13056, microseconds=724958), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: 微信', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 58, 263339, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13056, microseconds=858119), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: QQ', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 58, 396500, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13056, microseconds=962344), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理social应用: 微博', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 58, 500725, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=76852), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: Spotify', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 58, 615233, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=199457), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: Netflix', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 58, 737838, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=288762), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: 抖音', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 58, 827143, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=393784), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理media应用: 快手', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 58, 932165, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=502880), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理game应用: 王者荣耀', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 59, 41261, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=590405), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理game应用: 和平精英', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 59, 128786, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=699797), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理browser应用: Chrome浏览器', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 59, 238178, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=816288), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1847, 'message': '💪 强制停止顽固应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 59, 354669, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=816288), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_clear_apps_by_category', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2039, 'message': '✅ 清理browser应用: UC浏览器', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 59, 354669, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=816288), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_command_clear_all_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1888, 'message': '✅ 命令清理完成，清理了 22 个应用', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 59, 354669, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=817281), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2054, 'message': '💪 强制停止顽固应用...', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 59, 355662, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13057, microseconds=936070), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.google.android.apps.maps', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 59, 474451, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13058, microseconds=364000), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.google.android.apps.maps', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 5, 59, 902381, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13058, microseconds=488116), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.google.android.gms', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 0, 26497, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13058, microseconds=943706), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.google.android.gms', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 0, 482087, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13059, microseconds=98330), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.android.chrome', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 0, 636711, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13059, microseconds=508075), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.android.chrome', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 1, 46456, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13059, microseconds=614279), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.facebook.katana', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 1, 152660, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13060, microseconds=17595), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.facebook.katana', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 1, 555976, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13060, microseconds=131941), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.whatsapp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 1, 670322, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13060, microseconds=550476), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.whatsapp', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 2, 88857, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13060, microseconds=662547), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.tencent.mm', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 2, 200928, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13061, microseconds=71881), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.tencent.mm', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 2, 610262, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13061, microseconds=235425), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.netflix.mediaclient', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 2, 773806, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13061, microseconds=610299), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.netflix.mediaclient', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 3, 148680, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13061, microseconds=704302), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2083, 'message': '✅ 强制停止应用: com.spotify.music', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 3, 242683, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13062, microseconds=98223), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2096, 'message': '🔄 重置应用状态: com.spotify.music', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 3, 636604, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13062, microseconds=203168), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2111, 'message': '🧹 执行系统内存清理', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 3, 741549, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13062, microseconds=316740), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': '_force_stop_stubborn_apps', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 2118, 'message': '🗑️ 执行垃圾回收', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 3, 855121, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13062, microseconds=316740), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1857, 'message': '🎉 应用进程清理完成，共清理 30 个应用', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 3, 855121, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13064, microseconds=318238), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'clear_all_running_processes', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 301, 'message': '✅ 应用进程清理完成', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 5, 856619, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13064, microseconds=318750), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'start_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 214, 'message': '启动Ella应用', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 5, 857131, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13064, microseconds=401308), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'start_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 222, 'message': '尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 5, 939689, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13067, microseconds=642321), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_app_started', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 280, 'message': '✅ 应用已在前台: com.transsion.aivoiceassistant', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 9, 180702, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13067, microseconds=642321), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'start_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 227, 'message': '✅ Ella应用启动成功（指定Activity）', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 9, 180702, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13067, microseconds=643330), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'wait_for_page_load', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 301, 'message': '等待Ella页面加载完成 (超时: 15秒)', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 9, 181711, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13067, microseconds=838883), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'wait_for_page_load', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 311, 'message': '✅ 确认当前在Ella应用中', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 9, 377264, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13067, microseconds=919057), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 9, 457438, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13067, microseconds=968522), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'wait_for_page_load', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 320, 'message': '✅ 主输入框已出现，页面加载完成', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 9, 506903, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13067, microseconds=972052), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_load_status_check_config', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 287, 'message': '✅ 已加载状态检查配置，共 38 项', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 9, 510433, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13067, microseconds=969994), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'ella_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 333, 'message': '✅ Ella应用启动成功', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 9, 508375, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13067, microseconds=972052), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_detect_command_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 618, 'message': '检测到命令类型: google_maps (google地图应用状态)', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 9, 510433, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13067, microseconds=972052), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': 'check_app_opened', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 156, 'message': '检查maps应用状态', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 9, 510433, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13068, microseconds=648131), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'is_package_actively_running', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': 'com.google.android.apps.maps 未检测到活跃运行状态', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 10, 186512, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13069, microseconds=133049), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'is_package_actively_running', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': 'com.baidu.BaiduMap 未检测到活跃运行状态', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 10, 671430, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13069, microseconds=572654), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'is_package_actively_running', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': 'com.autonavi.minimap 未检测到活跃运行状态', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 11, 111035, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13069, microseconds=995417), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'is_package_actively_running', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': 'com.tencent.map 未检测到活跃运行状态', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 11, 533798, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=425204), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': 'check_app_opened', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 170, 'message': '未检测到maps应用', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 11, 963585, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
--- Logging error in Loguru Handler #4 ---
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=424237), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'is_package_actively_running', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': 'com.sogou.map.android.maps 未检测到活跃运行状态', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 11, 962618, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=426219), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'execute_command_and_verify', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 366, 'message': '初始状态False- 使用命令navigation to the first address in the image，状态: ', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 11, 964600, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
--- Logging error in Loguru Handler #4 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=426219), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_status_by_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 727, 'message': '获取google地图应用状态(初始): False', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 11, 964600, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=428227), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 769, 'message': '确保在对话页面...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 11, 966608, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=429214), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 11, 967595, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=632559), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 170940, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=633559), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': '检查对话页面指示器...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 171940, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
--- End of logging error ---
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=633559), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 171940, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=633559), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 171940, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=674587), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 212968, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=704153), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 812, 'message': '✅ 找到主输入框', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 242534, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=705585), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 780, 'message': '✅ 已在对话页面', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 243966, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=706627), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_ensure_input_box_ready', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 130, 'message': '确保输入框就绪...', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 245008, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=735297), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 273678, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=763191), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_check_known_input_elements', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 160, 'message': '✅ 找到主输入框', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 301572, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=764171), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': 'execute_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 34, 'message': '执行文本命令: navigation to the first address in the image', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 302552, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=764171), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_ensure_input_box_ready', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 130, 'message': '确保输入框就绪...', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 302552, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=792449), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 330830, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=824320), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_check_known_input_elements', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 160, 'message': '✅ 找到主输入框', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 362701, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=825310), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_input_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 230, 'message': '输入文本命令: navigation to the first address in the image', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 363691, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=858423), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 396804, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=909693), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 62, 'message': '等待元素出现 [输入框], 超时时间: 5秒', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 448074, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=951928), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 65, 'message': '元素列表 [True]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 490309, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13070, microseconds=952943), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 67, 'message': '元素已出现 [输入框]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 491324, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=69302), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'clear_text', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 325, 'message': '清空文本成功 [输入框]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 607683, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=128680), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 667061, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=159297), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 62, 'message': '等待元素出现 [输入框], 超时时间: 5秒', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 697678, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=191571), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 65, 'message': '元素列表 [True]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 729952, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=192574), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 67, 'message': '元素已出现 [输入框]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 730955, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=359575), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'send_keys', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 302, 'message': '输入文本成功 [输入框]: navigation to the first address in the image', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 897956, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=359575), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_input_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 247, 'message': '✅ 文本输入成功', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 897956, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=360587), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_send_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 321, 'message': '发送命令', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 898968, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=448568), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [发送按钮]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 12, 986949, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=608879), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 62, 'message': '等待元素出现 [发送按钮], 超时时间: 5秒', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 13, 147260, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=638960), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 65, 'message': '元素列表 [True]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 13, 177341, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=638960), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'wait_for_element', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 67, 'message': '元素已出现 [发送按钮]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 13, 177341, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=880825), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'click', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 231, 'message': '点击元素成功 [发送按钮]', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 13, 419206, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=882982), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_popup_tool', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 164, 'message': '✅ 弹窗处理工具初始化成功', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 13, 421363, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=881809), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': '_send_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 327, 'message': '✅ 点击发送按钮成功', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 13, 420190, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=881809), 'exception': None, 'extra': {}, 'file': (name='ella_command_executor.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_command_executor.py'), 'function': 'execute_text_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 53, 'message': '✅ 文本命令执行完成', 'module': 'ella_command_executor', 'name': 'pages.apps.ella.ella_command_executor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 13, 420190, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=881809), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_execute_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 870, 'message': '✅ 成功执行命令: navigation to the first address in the image', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 13, 420190, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=881809), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_handle_popup_after_command', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 178, 'message': 'handle_popup_after_command:处理弹窗', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 13, 420190, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13071, microseconds=882982), 'exception': None, 'extra': {}, 'file': (name='popup_tool.py', path='D:\\aigc\\app_test\\core\\popup_tool.py'), 'function': 'detect_and_close_popup_once', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 738, 'message': '执行单次弹窗检测和关闭', 'module': 'popup_tool', 'name': 'core.popup_tool', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 13, 421363, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13072, microseconds=796854), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_handle_popup_after_command', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 193, 'message': 'ℹ️ 未检测到弹窗或无需处理，命令: navigation to the first address in the image', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 14, 335235, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13072, microseconds=796854), 'exception': None, 'extra': {}, 'file': (name='popup_tool.py', path='D:\\aigc\\app_test\\core\\popup_tool.py'), 'function': 'detect_and_close_popup_once', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 742, 'message': '未检测到弹窗，无需处理', 'module': 'popup_tool', 'name': 'core.popup_tool', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 14, 335235, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13072, microseconds=796854), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_response_timeout', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1299, 'message': '📝 使用默认等待时间: 8秒', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 14, 335235, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13072, microseconds=799282), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'wait_for_response', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 35, 'message': '等待AI响应，超时时间: 8秒', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 14, 337663, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13073, microseconds=9545), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'12:06 am\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', "Zaïre-Emery on Chevalier\'s PSG Start", \'Dani, Bill: Daughter Crisis Sparks Reunion?\', \'What is Ask About Screen?\']...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 14, 547926, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13073, microseconds=231783), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', \'12:06 am\', "Hi, I\'m Ella", \'I can answer your questions, summarize content, and provide creative inspiration.\', \'Refresh\', "Zaïre-Emery on Chevalier\'s PSG Start", \'Dani, Bill: Daughter Crisis Sparks Reunion?\', \'What is Ask About Screen?\']...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 14, 770164, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13073, microseconds=395398), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'wait_for_response', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 60, 'message': '✅ 通过TTS按钮检测到响应', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 14, 933779, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Traceback (most recent call last):
Record was: {'elapsed': datetime.timedelta(seconds=13073, microseconds=395398), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_detect_command_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 618, 'message': '检测到命令类型: google_maps (google地图应用状态)', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 14, 933779, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13073, microseconds=396422), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_final_status_with_page_info', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 432, 'message': '等待状态变化: 3.0秒', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 14, 934803, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13076, microseconds=629584), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_final_status_with_page_info', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 440, 'message': '状态检查时当前应用包名: com.transsion.aivoiceassistant', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 18, 167965, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13076, microseconds=631112), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': 'check_app_opened', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 156, 'message': '检查maps应用状态', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 18, 169493, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13077, microseconds=242631), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'is_package_actively_running', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': 'com.google.android.apps.maps 未检测到活跃运行状态', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 18, 781012, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13077, microseconds=633866), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'is_package_actively_running', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': 'com.baidu.BaiduMap 未检测到活跃运行状态', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 19, 172247, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13078, microseconds=42077), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'is_package_actively_running', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': 'com.autonavi.minimap 未检测到活跃运行状态', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 19, 580458, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13078, microseconds=450601), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'is_package_actively_running', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': 'com.tencent.map 未检测到活跃运行状态', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 19, 988982, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13078, microseconds=881680), 'exception': None, 'extra': {}, 'file': (name='adb_process_monitor.py', path='D:\\aigc\\app_test\\tools\\adb_process_monitor.py'), 'function': 'is_package_actively_running', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': 'com.sogou.map.android.maps 未检测到活跃运行状态', 'module': 'adb_process_monitor', 'name': 'tools.adb_process_monitor', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 420061, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13078, microseconds=882265), 'exception': None, 'extra': {}, 'file': (name='app_detector.py', path='D:\\aigc\\app_test\\pages\\base\\app_detector.py'), 'function': 'check_app_opened', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 170, 'message': '未检测到maps应用', 'module': 'app_detector', 'name': 'pages.base.app_detector', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 420646, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13078, microseconds=882265), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_get_status_by_type', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 727, 'message': '获取google地图应用状态(最终): False', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 420646, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13078, microseconds=882265), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 558, 'message': '状态检查完成，现在获取响应文本', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 420646, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13078, microseconds=882770), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 562, 'message': '第1次尝试确保在Ella页面以获取响应', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 421151, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13078, microseconds=882770), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 769, 'message': '确保在对话页面...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 421151, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13078, microseconds=884107), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 422488, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=73328), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 611709, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=75527), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 808, 'message': '检查对话页面指示器...', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 613908, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
Traceback (most recent call last):
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=74330), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 612711, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=75527), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 613908, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=151630), 'exception': None, 'extra': {}, 'file': (name='base_element.py', path='D:\\aigc\\app_test\\core\\base_element.py'), 'function': 'is_exists', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 114, 'message': '元素存在性检查 [输入框]: True', 'module': 'base_element', 'name': 'core.base_element', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 690011, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=174326), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_check_chat_page_indicators', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 812, 'message': '✅ 找到主输入框', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 712707, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=175658), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'ensure_on_chat_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 780, 'message': '✅ 已在对话页面', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 714039, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
Traceback (most recent call last):
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=175658), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 566, 'message': '✅ 已确认在Ella对话页面，可以获取响应', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 714039, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=176671), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 715052, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=414124), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 952505, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=414124), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 952505, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=415118), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 953499, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=415118), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_ensure_on_ella_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1335, 'message': '检查是否在Ella页面...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 953499, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=415118), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2013, 'message': '检查当前进程是否是Ella...', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 20, 953499, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=580243), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从asr_txt节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 118624, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=579233), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2020, 'message': '当前应用: com.transsion.aivoiceassistant', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 117614, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=579233), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2021, 'message': '当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 117614, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=580243), 'exception': None, 'extra': {}, 'file': (name='system_status_checker.py', path='D:\\aigc\\app_test\\pages\\base\\system_status_checker.py'), 'function': 'ensure_ella_process', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 2030, 'message': '✅ 当前在Ella应用进程', 'module': 'system_status_checker', 'name': 'pages.base.system_status_checker', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 118624, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=580243), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_ensure_on_ella_page', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1344, 'message': '✅ 当前在Ella页面', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 118624, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=580243), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'get_response_all_text', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 140, 'message': '获取AI响应文本', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 118624, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=693797), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从asr_txt节点获取到原始文本: "navigation to the first address in the image"', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 232178, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=693797), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 474, 'message': '✅ 从asr_txt成功获取响应: navigation to the first address in the image', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 232178, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
--- End of logging error ---
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=693797), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'navigation to the first address in the image'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 232178, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=693797), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: navigation to the first address in the image', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 232178, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=695207), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从robot_text节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 233588, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13079, microseconds=756198), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'robot_text节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 294579, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13080, microseconds=257593), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从robot_text节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 795974, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13080, microseconds=302595), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'robot_text节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 21, 840976, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13080, microseconds=803436), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从robot_text节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 22, 341817, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13080, microseconds=877897), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'robot_text节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 22, 416278, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Record was: {'elapsed': datetime.timedelta(seconds=13080, microseconds=877897), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'robot_text节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 22, 416278, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
Traceback (most recent call last):
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13080, microseconds=877897), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 22, 416278, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13080, microseconds=905402), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 22, 443783, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13081, microseconds=406462), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 22, 944843, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13081, microseconds=450462), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 22, 988843, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13081, microseconds=951154), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_name节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 23, 489535, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13081, microseconds=972576), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_name节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 23, 510957, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13081, microseconds=973575), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'function_name节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 23, 511956, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13081, microseconds=973575), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 23, 511956, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13081, microseconds=997464), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 23, 535845, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13082, microseconds=498737), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 37118, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13082, microseconds=522611), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 60992, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=24015), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从function_control节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 562396, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=61149), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'function_control节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 599530, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=62135), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'function_control节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 600516, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=62135), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_card_chat_gpt节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 600516, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=217258), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从tv_card_chat_gpt节点获取到原始文本: "I am programmed to be a helpful and harmless AI assistant. Unfortunately, I am unable to perform this function currently."', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 755639, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=217258), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 474, 'message': '✅ 从tv_card_chat_gpt成功获取响应: I am programmed to be a helpful and harmless AI assistant. Unfortunately, I am unable to perform this function currently.', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 755639, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=217258), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'I am programmed to be a helpful and harmless AI assistant. Unfortunately, I am unable to perform this function currently.'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 755639, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=217258), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: I am programmed to be a helpful and harmless AI assistant. Unfortunately, I am unable to perform this function currently.', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 755639, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=217258), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_top节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 755639, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=382074), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 448, 'message': '从tv_top节点获取到原始文本: "Generated by AI, for reference only"', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 920455, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
--- Logging error in Loguru Handler #2 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=383076), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 474, 'message': '✅ 从tv_top成功获取响应: Generated by AI, for reference only', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 921457, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=382074), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'Generated by AI, for reference only'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 920455, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=382074), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: Generated by AI, for reference only', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 920455, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=383076), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 921457, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=422230), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 24, 960611, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=923628), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 25, 462009, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13083, microseconds=969547), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 25, 507928, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13084, microseconds=470488), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_banner节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 26, 8869, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13084, microseconds=521729), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_banner节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 26, 60110, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13084, microseconds=521729), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'tv_banner节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 26, 60110, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13084, microseconds=522725), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_text节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 26, 61106, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13084, microseconds=576447), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_text节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 26, 114828, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13085, microseconds=78547), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_text节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 26, 616928, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13085, microseconds=130307), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_text节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 26, 668688, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13085, microseconds=631522), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从tv_text节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 27, 169903, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13085, microseconds=664067), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'tv_text节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 27, 202448, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
--- Logging error in Loguru Handler #4 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
Record was: {'elapsed': datetime.timedelta(seconds=13085, microseconds=664067), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'tv_text节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 27, 202448, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13085, microseconds=664067), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 27, 202448, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13085, microseconds=689597), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 27, 227978, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13086, microseconds=190408), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 27, 728789, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13086, microseconds=241048), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 27, 779429, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13086, microseconds=742377), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_time_tv节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 28, 280758, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13086, microseconds=773129), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_time_tv节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 28, 311510, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
--- Logging error in Loguru Handler #2 ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
Record was: {'elapsed': datetime.timedelta(seconds=13086, microseconds=773129), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'alarm_time_tv节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 28, 311510, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13086, microseconds=774157), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 28, 312538, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13086, microseconds=801143), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 28, 339524, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13087, microseconds=302168), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 28, 840549, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13087, microseconds=334794), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 28, 873175, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13087, microseconds=835395), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 371, 'message': '尝试从alarm_day_tv节点获取响应 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 29, 373776, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13087, microseconds=885219), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 378, 'message': 'alarm_day_tv节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 29, 423600, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13087, microseconds=886218), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_text_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 383, 'message': 'alarm_day_tv节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 29, 424599, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13087, microseconds=886218), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第1次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 29, 424599, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13087, microseconds=921279), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第1次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 29, 459660, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13088, microseconds=422796), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第2次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 29, 961177, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13088, microseconds=456330), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第2次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 29, 994711, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13088, microseconds=957203), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 518, 'message': '尝试从alarm_switch节点获取checked属性 (第3次)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 495584, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13088, microseconds=987420), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 525, 'message': 'alarm_switch节点不存在 (第3次尝试)', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 525801, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13088, microseconds=987420), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_element_checked_with_retry', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 530, 'message': 'alarm_switch节点不存在，已达到最大重试次数', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 525801, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13088, microseconds=987420), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'get_response_all_text', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 168, 'message': '尝试获取其他有效的响应文本', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 525801, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13088, microseconds=988550), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_response_from_text_views', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1049, 'message': '从TextView元素获取响应', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 526931, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=167855), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_response_from_text_views', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1054, 'message': '从TextView获取响应: Dialogue', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 706236, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=166855), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1114, 'message': "正在验证文本是否为AI响应: 'Dialogue'", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 705236, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
Traceback (most recent call last):
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=167855), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'get_response_all_text', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 180, 'message': '✅ 获取到响应文本: Dialogue', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 706236, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=166855), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_is_ai_response', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 1193, 'message': '✅ 文本长度和内容合理，认为是有效响应: Dialogue', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 705236, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=167855), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_get_response_from_chat_list', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1065, 'message': '查找RecyclerView中的最新消息', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 706236, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=412139), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_attributes_from_xml', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 934, 'message': '正则提取到 15 个文本: [\'Dialogue\', \'Explore\', \'Swipe down to view earlier chats\', "Zaïre-Emery on Chevalier\'s PSG Start", \'Dani, Bill: Daughter Crisis Sparks Reunion?\', \'What is Ask About Screen?\', \'navigation to the first address in the image\', \'I am programmed to be a helpful and harmless AI assistant. Unfortunately, I am unable to perform this function currently.\', \'Generated by AI, for reference only\', \'Can you suggest alternatives?\']...', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 950520, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=412139), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': '_extract_text_from_check_area_dump', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 897, 'message': "从dump正则提取文本: Dialogue Explore Swipe down to view earlier chats Zaïre-Emery on Chevalier's PSG Start Dani, Bill: Daughter Crisis Sparks Reunion? What is Ask About Screen? navigation to the first address in the image I am programmed to be a helpful and harmless AI assistant. Unfortunately, I am unable to perform this function currently. Generated by AI, for reference only Can you suggest alternatives? Why can't you navigate? I need navigation assistance. DeepSeek-R1 Feel free to ask me any questions… 12:06", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 950520, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.log' -> 'D:\\aigc\\app_test\\logs\\debug\\debug_20250826.2025-08-26_13-17-02_169256.log'
--- End of logging error ---
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=412139), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'get_response_all_text', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 180, 'message': "✅ 获取到响应文本: Dialogue Explore Swipe down to view earlier chats Zaïre-Emery on Chevalier's PSG Start Dani, Bill: Daughter Crisis Sparks Reunion? What is Ask About Screen? navigation to the first address in the image I am programmed to be a helpful and harmless AI assistant. Unfortunately, I am unable to perform this function currently. Generated by AI, for reference only Can you suggest alternatives? Why can't you navigate? I need navigation assistance. DeepSeek-R1 Feel free to ask me any questions… 12:06", 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 950520, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=413137), 'exception': None, 'extra': {}, 'file': (name='ella_response_handler.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\ella_response_handler.py'), 'function': 'get_response_all_text', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 185, 'message': '未获取到有效的响应文本', 'module': 'ella_response_handler', 'name': 'pages.apps.ella.ella_response_handler', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 951518, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=413137), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': '_wait_and_get_response_after_status_check', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 596, 'message': '最终获取的AI响应: \'[\'navigation to the first address in the image\', \'\', \'\', \'\', \'I am programmed to be a helpful and harmless AI assistant. Unfortunately, I am unable to perform this function currently.\', \'Generated by AI, for reference only\', \'\', \'\', \'\', \'\', \'\', \'Dialogue\', "Dialogue Explore Swipe down to view earlier chats Zaïre-Emery on Chevalier\'s PSG Start Dani, Bill: Daughter Crisis Sparks Reunion? What is Ask About Screen? navigation to the first address in the image I am programmed to be a helpful and harmless AI assistant. Unfortunately, I am unable to perform this function currently. Generated by AI, for reference only Can you suggest alternatives? Why can\'t you navigate? I need navigation assistance. DeepSeek-R1 Feel free to ask me any questions… 12:06"]\'', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 30, 951518, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=588780), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'simple_command_test', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1374, 'message': '🎉 navigation to the first address in the image 测试完成', 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 31, 127161, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=588780), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response_advanced', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1090, 'message': "verify_expected_in_response_advanced 响应类型: <class 'list'>, 搜索模式: combined, 匹配模式: 任意匹配", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 31, 127161, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=589830), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response_advanced', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1158, 'message': "⚠️ 未找到期望内容: 'Sorry'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 31, 128211, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=589830), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response_advanced', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1158, 'message': "⚠️ 未找到期望内容: 'Oops'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 31, 128211, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=589830), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response_advanced', 'level': (name='WARNING', no=30, icon='⚠️'), 'line': 1158, 'message': "⚠️ 未找到期望内容: 'out of my reach'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 31, 128211, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=590847), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response_advanced', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1133, 'message': "✅ [合并模式] 找到期望内容: 'Generated by AI, for reference only'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 31, 129228, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=590847), 'exception': None, 'extra': {}, 'file': (name='base_ella_test.py', path='D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py'), 'function': 'verify_expected_in_response_advanced', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 1154, 'message': "🎉 [任意匹配模式] 找到期望内容，验证通过: 'Generated by AI, for reference only'", 'module': 'base_ella_test', 'name': 'testcases.test_ella.base_ella_test', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 31, 129228, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #2 ---
Record was: {'elapsed': datetime.timedelta(seconds=13089, microseconds=769002), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': 'stop_app', 'level': (name='INFO', no=20, icon='ℹ️'), 'line': 388, 'message': '停止Ella应用', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 31, 307383, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
Traceback (most recent call last):
  File "D:\SystemApplication\Lib\site-packages\loguru\_handler.py", line 315, in _queued_writer
    self._sink.write(message)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 204, in write
    self._terminate_file(is_rotating=True)
  File "D:\SystemApplication\Lib\site-packages\loguru\_file_sink.py", line 276, in _terminate_file
    os.rename(old_path, renamed_path)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'D:\\aigc\\app_test\\logs\\general\\app_20250826.log' -> 'D:\\aigc\\app_test\\logs\\general\\app_20250826.2025-08-26_13-17-02_145748.log'
--- End of logging error ---
--- Logging error in Loguru Handler #4 ---
Record was: {'elapsed': datetime.timedelta(seconds=13091, microseconds=91416), 'exception': None, 'extra': {}, 'file': (name='dialogue_page.py', path='D:\\aigc\\app_test\\pages\\apps\\ella\\dialogue_page.py'), 'function': '_verify_app_stopped', 'level': (name='DEBUG', no=10, icon='🐞'), 'line': 426, 'message': '当前前台应用: com.transsion.settings.nfc，目标应用已不在前台', 'module': 'dialogue_page', 'name': 'pages.apps.ella.dialogue_page', 'process': (id=88492, name='MainProcess'), 'thread': (id=35480, name='MainThread'), 'time': datetime(2025, 8, 27, 0, 6, 32, 629797, tzinfo=datetime.timezone(datetime.timedelta(seconds=28800), '中国标准时间'))}
