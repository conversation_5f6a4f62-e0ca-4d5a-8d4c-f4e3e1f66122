"""
TTS语音合成工具
支持多种TTS服务，将文本转换为语音并通过麦克风播放
"""
import os
import sys
import time
import tempfile
import threading
from pathlib import Path
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import log


class TTSManager:
    """TTS语音合成管理器"""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir()) / "app_test_tts"
        self.temp_dir.mkdir(exist_ok=True)

        # 语音文件存储根目录 - 相对于项目根目录
        self.project_root = self._get_project_root()
        self.data_dir = self.project_root / "data" / "tts"
        self.data_dir.mkdir(parents=True, exist_ok=True)

        log.debug(f"TTS数据目录: {self.data_dir.absolute()}")

        # 语言代码映射
        self.language_mapping = {
            'zh-CN': 'zh',
            'zh-TW': 'zh',
            'zh-HK': 'zh',
            'zh': 'zh',
            'en-US': 'en',
            'en-GB': 'en',
            'en-AU': 'en',
            'en': 'en',
            'ja-JP': 'ja',
            'ja': 'ja',
            'ko-KR': 'ko',
            'ko': 'ko',
            'fr-FR': 'fr',
            'fr': 'fr',
            'de-DE': 'de',
            'de': 'de',
            'es-ES': 'es',
            'es': 'es',
            'it-IT': 'it',
            'it': 'it',
            'pt-PT': 'pt',
            'pt': 'pt',
            'ru-RU': 'ru',
            'ru': 'ru'
        }
        
        # 支持的TTS服务配置
        self.tts_services = {
            'edge_tts': {
                'name': 'Microsoft Edge TTS',
                'available': self._check_edge_tts(),
                'voices': {
                    'zh-CN': 'zh-CN-XiaoxiaoNeural',
                    'en-US': 'en-US-AriaNeural'
                }
            },
            'gtts': {
                'name': 'Google Text-to-Speech',
                'available': self._check_gtts(),
                'voices': {
                    'zh-CN': 'zh',
                    'en-US': 'en'
                }
            },
            'pyttsx3': {
                'name': 'Offline TTS (pyttsx3)',
                'available': self._check_pyttsx3(),
                'voices': {}
            }
        }
        
        # 选择可用的TTS服务
        self.selected_service = self._select_best_service()
        log.info(f"选择的TTS服务: {self.selected_service}")

    def _get_project_root(self) -> Path:
        """
        获取项目根目录

        Returns:
            Path: 项目根目录路径
        """
        # 从当前文件位置向上查找项目根目录
        current_file = Path(__file__).resolve()

        # 查找包含特定标识文件的目录作为项目根目录
        project_indicators = [
            'requirements.txt',
            'setup.py',
            'pyproject.toml',
            '.git',
            'core',  # 我们的core目录
            'pages', # 我们的pages目录
            'testcases' # 我们的testcases目录
        ]

        # 从当前文件目录开始向上查找
        search_dir = current_file.parent
        max_levels = 5  # 最多向上查找5级目录

        for _ in range(max_levels):
            # 检查是否包含项目标识文件/目录
            for indicator in project_indicators:
                if (search_dir / indicator).exists():
                    log.debug(f"找到项目根目录: {search_dir} (标识: {indicator})")
                    return search_dir

            # 向上一级目录
            parent = search_dir.parent
            if parent == search_dir:  # 已到达根目录
                break
            search_dir = parent

        # 如果没找到，使用当前工作目录
        cwd = Path.cwd()
        log.warning(f"未找到项目根目录，使用当前工作目录: {cwd}")
        return cwd

    def _get_language_code(self, language: str) -> str:
        """
        获取标准化的语言代码

        Args:
            language: 输入的语言代码

        Returns:
            str: 标准化的语言代码
        """
        # 先尝试原始大小写，再尝试小写
        lang_code = self.language_mapping.get(language)
        if lang_code is None:
            lang_code = self.language_mapping.get(language.lower(), 'en')

        log.debug(f"语言代码映射: {language} -> {lang_code}")
        return lang_code

    def _get_language_dir(self, language: str) -> Path:
        """
        获取语言对应的存储目录

        Args:
            language: 语言代码

        Returns:
            Path: 语言目录路径
        """
        lang_code = self._get_language_code(language)
        lang_dir = self.data_dir / lang_code
        lang_dir.mkdir(exist_ok=True)
        return lang_dir

    def _generate_filename(self, text: str, language: str, extension: str = '.wav') -> str:
        """
        根据文本内容生成文件名

        Args:
            text: 文本内容
            language: 语言代码
            extension: 文件扩展名

        Returns:
            str: 生成的文件名
        """
        import re
        import hashlib

        # 清理文本，只保留字母数字和空格
        clean_text = re.sub(r'[^\w\s]', '', text)
        clean_text = re.sub(r'\s+', '_', clean_text.strip())

        # 限制文件名长度
        if len(clean_text) > 50:
            # 如果文本太长，使用前30个字符 + 哈希值
            text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()[:8]
            clean_text = clean_text[:30] + '_' + text_hash

        # 确保文件名不为空
        if not clean_text:
            text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()[:12]
            clean_text = f"tts_{text_hash}"

        return f"{clean_text}{extension}"

    def get_audio_file_path(self, text: str, language: str = 'zh-CN', relative: bool = True) -> Path:
        """
        获取音频文件的路径

        Args:
            text: 文本内容
            language: 语言代码
            relative: 是否返回相对路径

        Returns:
            Path: 音频文件路径
        """
        lang_dir = self._get_language_dir(language)
        filename = self._generate_filename(text, language)
        full_path = lang_dir / filename

        if relative:
            # 返回相对于项目根目录的路径
            try:
                relative_path = full_path.relative_to(self.project_root)
                # 统一使用正斜杠作为路径分隔符
                return Path(str(relative_path).replace('\\', '/'))
            except ValueError:
                # 如果无法计算相对路径，返回相对于当前目录的路径
                try:
                    rel_path = full_path.relative_to(Path.cwd())
                    return Path(str(rel_path).replace('\\', '/'))
                except ValueError:
                    # 最后回退到绝对路径
                    return full_path
        else:
            return full_path
    
    def _check_edge_tts(self) -> bool:
        """检查Edge TTS是否可用"""
        try:
            import edge_tts
            return True
        except ImportError:
            return False
    
    def _check_gtts(self) -> bool:
        """检查Google TTS是否可用"""
        try:
            from gtts import gTTS
            return True
        except ImportError:
            return False
    
    def _check_pyttsx3(self) -> bool:
        """检查pyttsx3是否可用"""
        try:
            import pyttsx3
            return True
        except ImportError:
            return False
    
    def _select_best_service(self) -> str:
        """选择最佳的TTS服务"""
        # 优先级: edge_tts > gtts > pyttsx3
        for service in ['edge_tts', 'gtts', 'pyttsx3']:
            if self.tts_services[service]['available']:
                return service
        return None
    
    def install_dependencies(self) -> bool:
        """安装TTS依赖"""
        try:
            import subprocess
            
            # 安装常用的TTS库
            packages = [
                'edge-tts',      # Microsoft Edge TTS
                'gtts',          # Google Text-to-Speech
                'pyttsx3',       # Offline TTS
                'pygame',        # 音频播放
                'playsound',     # 简单音频播放
            ]
            
            log.info("安装TTS依赖包...")
            for package in packages:
                try:
                    result = subprocess.run([
                        sys.executable, '-m', 'pip', 'install', package
                    ], capture_output=True, text=True, timeout=60)
                    
                    if result.returncode == 0:
                        log.info(f"✅ 成功安装: {package}")
                    else:
                        log.warning(f"⚠️ 安装失败: {package}")
                        
                except subprocess.TimeoutExpired:
                    log.warning(f"⚠️ 安装超时: {package}")
                except Exception as e:
                    log.warning(f"⚠️ 安装异常: {package}, 错误: {e}")
            
            # 重新检查可用性
            self.tts_services['edge_tts']['available'] = self._check_edge_tts()
            self.tts_services['gtts']['available'] = self._check_gtts()
            self.tts_services['pyttsx3']['available'] = self._check_pyttsx3()
            
            # 重新选择服务
            self.selected_service = self._select_best_service()
            
            return self.selected_service is not None
            
        except Exception as e:
            log.error(f"安装TTS依赖失败: {e}")
            return False
    
    def text_to_speech(self, text: str, language: str = 'zh-CN',
                      output_file: Optional[str] = None, use_cache: bool = True) -> Optional[str]:
        """
        将文本转换为语音文件（支持按语言分类存储）

        Args:
            text: 要转换的文本
            language: 语言代码 (zh-CN, en-US)
            output_file: 输出文件路径，如果为None则自动生成
            use_cache: 是否使用缓存（检查已存在的文件）

        Returns:
            str: 生成的音频文件路径，失败返回None
        """
        if not self.selected_service:
            log.error("没有可用的TTS服务")
            return None

        # 如果没有指定输出文件，则使用分类存储
        if not output_file:
            if use_cache:
                # 检查是否已有缓存文件（使用绝对路径进行检查）
                cached_file = self.get_audio_file_path(text, language, relative=False)
                if cached_file.exists() and self._verify_audio_file(str(cached_file)):
                    log.info(f"🎯 使用缓存文件: {cached_file}")
                    return str(cached_file)
                output_file = str(cached_file)
            else:
                # 使用临时文件
                output_file = self.temp_dir / f"tts_{int(time.time())}.wav"

        try:
            if self.selected_service == 'edge_tts':
                return self._edge_tts_generate(text, language, str(output_file))
            elif self.selected_service == 'gtts':
                return self._gtts_generate(text, language, str(output_file))
            elif self.selected_service == 'pyttsx3':
                return self._pyttsx3_generate(text, language, str(output_file))
            else:
                log.error(f"不支持的TTS服务: {self.selected_service}")
                return None

        except Exception as e:
            log.error(f"TTS转换失败: {e}")
            return None
    
    def _edge_tts_generate(self, text: str, language: str, output_file: str) -> Optional[str]:
        """使用Edge TTS生成语音，确保文件完整保存"""
        try:
            import edge_tts
            import asyncio

            voice = self.tts_services['edge_tts']['voices'].get(language, 'zh-CN-XiaoxiaoNeural')
            log.info(f"🎤 使用Edge TTS生成语音: '{text}' (语音: {voice})")

            # 确保输出目录存在
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 如果文件已存在，先删除
            if output_path.exists():
                try:
                    output_path.unlink()
                    log.debug(f"删除已存在的文件: {output_file}")
                except Exception as e:
                    log.warning(f"删除已存在文件失败: {e}")

            async def generate():
                communicate = edge_tts.Communicate(text, voice)
                await communicate.save(str(output_file))

            # 运行异步函数
            log.debug("开始Edge TTS异步生成...")
            asyncio.run(generate())

            # 验证文件是否成功生成
            if self._verify_audio_file(output_file):
                log.info(f"✅ Edge TTS生成成功: {output_file}")
                return str(output_file)
            else:
                log.error("Edge TTS生成的文件验证失败")
                return None

        except Exception as e:
            log.error(f"Edge TTS生成异常: {e}")
            return None
    
    def _gtts_generate(self, text: str, language: str, output_file: str) -> Optional[str]:
        """使用Google TTS生成语音，确保文件完整保存"""
        try:
            from gtts import gTTS

            lang_code = self.tts_services['gtts']['voices'].get(language, 'zh')
            log.info(f"🎤 使用Google TTS生成语音: '{text}' (语言: {lang_code})")

            # 确保输出目录存在
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 如果文件已存在，先删除
            if output_path.exists():
                try:
                    output_path.unlink()
                    log.debug(f"删除已存在的文件: {output_file}")
                except Exception as e:
                    log.warning(f"删除已存在文件失败: {e}")

            # 生成TTS
            log.debug("开始Google TTS生成...")
            tts = gTTS(text=text, lang=lang_code, slow=False)
            tts.save(str(output_file))

            # 验证文件是否成功生成
            if self._verify_audio_file(output_file):
                log.info(f"✅ Google TTS生成成功: {output_file}")
                return str(output_file)
            else:
                log.error("Google TTS生成的文件验证失败")
                return None

        except Exception as e:
            log.error(f"Google TTS生成异常: {e}")
            return None
    
    def _pyttsx3_generate(self, text: str, language: str, output_file: str) -> Optional[str]:
        """使用pyttsx3生成语音，确保文件完整保存"""
        try:
            import pyttsx3

            log.info(f"🎤 使用pyttsx3生成语音: '{text}' (离线TTS)")

            # 确保输出目录存在
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 如果文件已存在，先删除
            if output_path.exists():
                try:
                    output_path.unlink()
                    log.debug(f"删除已存在的文件: {output_file}")
                except Exception as e:
                    log.warning(f"删除已存在文件失败: {e}")

            # 初始化TTS引擎
            log.debug("初始化pyttsx3引擎...")
            engine = pyttsx3.init()

            # 设置语音参数
            voices = engine.getProperty('voices')
            selected_voice = None
            if voices:
                # 尝试选择合适的语音
                for voice in voices:
                    if language.startswith('zh') and ('chinese' in voice.name.lower() or 'zh' in voice.id.lower()):
                        selected_voice = voice.id
                        engine.setProperty('voice', voice.id)
                        log.debug(f"选择中文语音: {voice.name}")
                        break
                    elif language.startswith('en') and ('english' in voice.name.lower() or 'en' in voice.id.lower()):
                        selected_voice = voice.id
                        engine.setProperty('voice', voice.id)
                        log.debug(f"选择英文语音: {voice.name}")
                        break

            # 设置语速和音量
            engine.setProperty('rate', 150)  # 语速
            engine.setProperty('volume', 0.9)  # 音量

            # 保存到文件
            log.debug("开始pyttsx3生成...")
            engine.save_to_file(text, str(output_file))
            engine.runAndWait()

            # 等待文件生成完成
            time.sleep(0.5)

            # 验证文件是否成功生成
            if self._verify_audio_file(output_file):
                log.info(f"✅ pyttsx3生成成功: {output_file}")
                return str(output_file)
            else:
                log.error("pyttsx3生成的文件验证失败")
                return None

        except Exception as e:
            log.error(f"pyttsx3生成异常: {e}")
            return None

    def _verify_audio_file(self, audio_file: str, min_size_bytes: int = 1024) -> bool:
        """
        验证音频文件是否有效（优化版本）

        Args:
            audio_file: 音频文件路径
            min_size_bytes: 最小文件大小（字节）

        Returns:
            bool: 文件是否有效
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(audio_file):
                log.debug(f"音频文件不存在: {audio_file}")
                return False

            # 等待文件写入完成（防止文件正在写入中）
            max_wait_time = 3  # 最大等待3秒
            wait_interval = 0.2  # 每次等待0.2秒
            waited_time = 0

            last_size = 0
            stable_count = 0

            while waited_time < max_wait_time:
                try:
                    current_size = os.path.getsize(audio_file)
                    if current_size == last_size and current_size > 0:
                        stable_count += 1
                        if stable_count >= 2:  # 连续2次大小相同，认为稳定
                            break
                    else:
                        stable_count = 0
                    last_size = current_size
                    time.sleep(wait_interval)
                    waited_time += wait_interval
                except OSError:
                    # 文件可能正在写入，继续等待
                    time.sleep(wait_interval)
                    waited_time += wait_interval

            # 检查文件大小
            file_size = os.path.getsize(audio_file)
            if file_size < min_size_bytes:
                log.debug(f"音频文件太小: {audio_file} ({file_size} bytes)")
                return False

            # 检查文件扩展名
            file_ext = Path(audio_file).suffix.lower()
            valid_extensions = ['.wav', '.mp3', '.ogg', '.m4a', '.aac']
            if file_ext not in valid_extensions:
                log.debug(f"不支持的音频格式: {file_ext}")
                return False

            # 简化的文件头部验证
            try:
                with open(audio_file, 'rb') as f:
                    header = f.read(12)  # 读取前12字节
                    if len(header) < 4:
                        log.debug(f"音频文件头部太短: {audio_file}")
                        return False

                    # 检查WAV文件头（更宽松的检查）
                    if file_ext == '.wav':
                        if header[:4] == b'RIFF':
                            log.debug(f"WAV文件头部验证通过: {audio_file}")
                        else:
                            log.debug(f"WAV文件头部可能异常，但继续: {audio_file}")
                            # 不直接返回False，继续验证

                    # 检查MP3文件头（更宽松的检查）
                    elif file_ext == '.mp3':
                        if header[:3] == b'ID3' or header[:2] in [b'\xff\xfb', b'\xff\xfa']:
                            log.debug(f"MP3文件头部验证通过: {audio_file}")
                        else:
                            log.debug(f"MP3文件头部可能异常，但继续: {audio_file}")
                            # 不直接返回False，继续验证

            except Exception as e:
                log.debug(f"读取音频文件头部失败，但文件存在: {e}")
                # 如果文件存在且大小合适，即使头部读取失败也认为有效
                pass

            log.debug(f"音频文件验证通过: {audio_file} ({file_size} bytes)")
            return True

        except Exception as e:
            log.debug(f"音频文件验证异常: {e}")
            return False

    def play_audio(self, audio_file: str, volume: float = 1.0) -> bool:
        """
        播放音频文件，播放前验证文件完整性

        Args:
            audio_file: 音频文件路径
            volume: 音量 (0.0-1.0)

        Returns:
            bool: 播放是否成功
        """
        # 验证音频文件
        if not self._verify_audio_file(audio_file):
            log.error(f"音频文件验证失败: {audio_file}")
            return False

        file_size = os.path.getsize(audio_file) / 1024  # KB
        log.info(f"🔊 开始播放音频: {Path(audio_file).name} ({file_size:.1f}KB)")

        try:
            # 尝试多种播放方法，按优先级排序
            success = (self._play_with_pygame(audio_file, volume) or
                      self._play_with_playsound(audio_file) or
                      self._play_with_system(audio_file))

            if success:
                log.info(f"✅ 音频播放完成: {Path(audio_file).name}")
            else:
                log.error(f"❌ 所有播放方法都失败: {audio_file}")

            return success

        except Exception as e:
            log.error(f"播放音频异常: {e}")
            return False
    
    def _play_with_pygame(self, audio_file: str, volume: float) -> bool:
        """使用pygame播放音频"""
        try:
            import pygame
            
            pygame.mixer.init()
            pygame.mixer.music.load(audio_file)
            pygame.mixer.music.set_volume(volume)
            pygame.mixer.music.play()
            
            # 等待播放完成
            while pygame.mixer.music.get_busy():
                time.sleep(0.1)
            
            pygame.mixer.quit()
            log.info("✅ pygame播放完成")
            return True
            
        except ImportError:
            log.debug("pygame不可用")
            return False
        except Exception as e:
            log.debug(f"pygame播放失败: {e}")
            return False
    
    def _play_with_playsound(self, audio_file: str) -> bool:
        """使用playsound播放音频"""
        try:
            from playsound import playsound
            
            playsound(audio_file)
            log.info("✅ playsound播放完成")
            return True
            
        except ImportError:
            log.debug("playsound不可用")
            return False
        except Exception as e:
            log.debug(f"playsound播放失败: {e}")
            return False
    
    def _play_with_system(self, audio_file: str) -> bool:
        """使用系统命令播放音频"""
        try:
            import subprocess
            import platform
            
            system = platform.system().lower()
            
            if system == 'windows':
                # Windows使用内置播放器
                subprocess.run(['start', '/wait', audio_file], shell=True, check=True)
            elif system == 'darwin':  # macOS
                subprocess.run(['afplay', audio_file], check=True)
            elif system == 'linux':
                # Linux尝试多种播放器
                players = ['aplay', 'paplay', 'play', 'mpg123']
                for player in players:
                    try:
                        subprocess.run([player, audio_file], check=True, 
                                     capture_output=True, timeout=30)
                        break
                    except (subprocess.CalledProcessError, FileNotFoundError):
                        continue
                else:
                    return False
            else:
                log.warning(f"不支持的系统: {system}")
                return False
            
            log.info("✅ 系统播放器播放完成")
            return True
            
        except Exception as e:
            log.debug(f"系统播放失败: {e}")
            return False
    
    def speak_text(self, text: str, language: str = 'zh-CN',
                  volume: float = 1.0, cleanup: bool = True, use_cache: bool = True) -> bool:
        """
        直接朗读文本（TTS + 播放），支持缓存版本

        Args:
            text: 要朗读的文本
            language: 语言代码
            volume: 音量
            cleanup: 是否清理临时文件
            use_cache: 是否使用缓存文件

        Returns:
            bool: 朗读是否成功
        """
        start_time = time.time()
        log.info(f"🎤 开始朗读文本: '{text}' (语言: {language}, 音量: {volume})")

        try:
            # 生成语音文件
            log.debug("步骤1: 生成语音文件...")
            audio_file = self.text_to_speech(text, language, use_cache=use_cache)
            if not audio_file:
                log.error("❌ 语音生成失败")
                return False

            generation_time = time.time() - start_time
            log.debug(f"语音生成耗时: {generation_time:.2f}秒")

            # 播放语音
            log.debug("步骤2: 播放语音文件...")
            play_start_time = time.time()
            success = self.play_audio(audio_file, volume)
            play_time = time.time() - play_start_time

            if success:
                log.debug(f"音频播放耗时: {play_time:.2f}秒")

            # 清理临时文件
            if cleanup and os.path.exists(audio_file):
                try:
                    log.debug("步骤3: 清理临时文件...")
                    os.remove(audio_file)
                    log.debug(f"✅ 清理临时文件: {Path(audio_file).name}")
                except Exception as e:
                    log.warning(f"⚠️ 清理临时文件失败: {e}")
            elif not cleanup:
                log.debug(f"保留临时文件: {audio_file}")

            total_time = time.time() - start_time

            if success:
                log.info(f"✅ 文本朗读完成: '{text}' (总耗时: {total_time:.2f}秒)")
            else:
                log.error(f"❌ 文本朗读失败: '{text}' (耗时: {total_time:.2f}秒)")

            return success

        except Exception as e:
            total_time = time.time() - start_time
            log.error(f"❌ 文本朗读异常: '{text}' - {e} (耗时: {total_time:.2f}秒)")
            return False

    def generate_audio_file(self, text: str, output_path: str = None, language: str = 'zh-CN') -> bool:
        """
        生成音频文件并保存到指定路径（不自动清理）

        Args:
            text: 要转换的文本
            output_path: 输出文件路径，如果为None则使用默认分类路径
            language: 语言代码

        Returns:
            bool: 生成是否成功
        """
        # 如果没有指定输出路径，使用默认的分类路径
        if output_path is None:
            output_path = self.get_audio_file_path(text, language, relative=False)
        else:
            # 如果指定了路径，确保它是相对于项目根目录的
            output_file = Path(output_path)
            if not output_file.is_absolute():
                output_file = self.project_root / output_file
            output_path = output_file

        # 显示相对路径用于日志
        try:
            relative_path = Path(output_path).relative_to(self.project_root)
            log.info(f"📁 生成音频文件: '{text}' -> {relative_path}")
        except ValueError:
            log.info(f"📁 生成音频文件: '{text}' -> {output_path}")

        try:
            # 确保输出目录存在
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            # 生成音频文件
            audio_file = self.text_to_speech(text, language, str(output_file), use_cache=False)

            if audio_file and self._verify_audio_file(audio_file):
                file_size = os.path.getsize(audio_file) / 1024  # KB
                try:
                    relative_path = Path(audio_file).relative_to(self.project_root)
                    log.info(f"✅ 音频文件生成成功: {relative_path} ({file_size:.1f}KB)")
                except ValueError:
                    log.info(f"✅ 音频文件生成成功: {audio_file} ({file_size:.1f}KB)")
                return True
            else:
                log.error(f"❌ 音频文件生成失败: {output_path}")
                return False

        except Exception as e:
            log.error(f"❌ 生成音频文件异常: {e}")
            return False

    def play_audio_file(self, audio_path: str, volume: float = 1.0) -> bool:
        """
        播放指定路径的音频文件

        Args:
            audio_path: 音频文件路径
            volume: 音量

        Returns:
            bool: 播放是否成功
        """
        if not os.path.exists(audio_path):
            log.error(f"音频文件不存在: {audio_path}")
            return False

        return self.play_audio(audio_path, volume)

    def list_cached_files(self, language: str = None) -> Dict[str, list]:
        """
        列出缓存的音频文件

        Args:
            language: 指定语言，如果为None则列出所有语言

        Returns:
            Dict[str, list]: 按语言分组的文件列表
        """
        cached_files = {}

        if language:
            # 列出指定语言的文件
            lang_code = self._get_language_code(language)
            lang_dir = self.data_dir / lang_code
            if lang_dir.exists():
                files = []
                for file_path in lang_dir.glob("*.wav"):
                    if self._verify_audio_file(str(file_path)):
                        file_info = {
                            'name': file_path.name,
                            'path': str(file_path),
                            'size': file_path.stat().st_size / 1024,  # KB
                            'modified': file_path.stat().st_mtime
                        }
                        files.append(file_info)
                cached_files[lang_code] = files
        else:
            # 列出所有语言的文件
            for lang_dir in self.data_dir.iterdir():
                if lang_dir.is_dir():
                    files = []
                    for file_path in lang_dir.glob("*.wav"):
                        if self._verify_audio_file(str(file_path)):
                            file_info = {
                                'name': file_path.name,
                                'path': str(file_path),
                                'size': file_path.stat().st_size / 1024,  # KB
                                'modified': file_path.stat().st_mtime
                            }
                            files.append(file_info)
                    if files:
                        cached_files[lang_dir.name] = files

        return cached_files

    def clear_cache(self, language: str = None, confirm: bool = False) -> bool:
        """
        清理缓存文件

        Args:
            language: 指定语言，如果为None则清理所有语言
            confirm: 是否确认删除

        Returns:
            bool: 清理是否成功
        """
        if not confirm:
            log.warning("清理缓存需要确认，请设置 confirm=True")
            return False

        try:
            deleted_count = 0

            if language:
                # 清理指定语言
                lang_code = self._get_language_code(language)
                lang_dir = self.data_dir / lang_code
                if lang_dir.exists():
                    for file_path in lang_dir.glob("*.wav"):
                        try:
                            file_path.unlink()
                            deleted_count += 1
                            log.debug(f"删除缓存文件: {file_path}")
                        except Exception as e:
                            log.warning(f"删除文件失败: {file_path} - {e}")
            else:
                # 清理所有语言
                for lang_dir in self.data_dir.iterdir():
                    if lang_dir.is_dir():
                        for file_path in lang_dir.glob("*.wav"):
                            try:
                                file_path.unlink()
                                deleted_count += 1
                                log.debug(f"删除缓存文件: {file_path}")
                            except Exception as e:
                                log.warning(f"删除文件失败: {file_path} - {e}")

            log.info(f"✅ 清理完成，删除了 {deleted_count} 个缓存文件")
            return True

        except Exception as e:
            log.error(f"❌ 清理缓存失败: {e}")
            return False

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        stats = {
            'total_files': 0,
            'total_size': 0,  # KB
            'languages': {},
            'data_dir': str(self.data_dir)
        }

        try:
            for lang_dir in self.data_dir.iterdir():
                if lang_dir.is_dir():
                    lang_stats = {
                        'files': 0,
                        'size': 0,
                        'files_list': []
                    }

                    for file_path in lang_dir.glob("*.wav"):
                        if self._verify_audio_file(str(file_path)):
                            file_size = file_path.stat().st_size / 1024  # KB
                            lang_stats['files'] += 1
                            lang_stats['size'] += file_size
                            lang_stats['files_list'].append({
                                'name': file_path.name,
                                'size': file_size
                            })

                    if lang_stats['files'] > 0:
                        stats['languages'][lang_dir.name] = lang_stats
                        stats['total_files'] += lang_stats['files']
                        stats['total_size'] += lang_stats['size']

        except Exception as e:
            log.error(f"获取缓存统计失败: {e}")

        return stats

    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            if self.temp_dir.exists():
                for file in self.temp_dir.glob("tts_*.wav"):
                    try:
                        file.unlink()
                        log.debug(f"清理临时文件: {file}")
                    except Exception as e:
                        log.debug(f"清理文件失败: {file}, 错误: {e}")
        except Exception as e:
            log.debug(f"清理临时目录失败: {e}")
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取TTS服务信息"""
        return {
            'selected_service': self.selected_service,
            'available_services': {
                name: info['available'] 
                for name, info in self.tts_services.items()
            },
            'service_details': self.tts_services
        }


# 全局TTS管理器实例
tts_manager = TTSManager()


def speak_text(text: str, language: str = 'zh-CN', volume: float = 1.0, use_cache: bool = True) -> bool:
    """
    便捷的文本朗读函数（支持缓存）

    Args:
        text: 要朗读的文本
        language: 语言代码 (zh-CN, en-US)
        volume: 音量 (0.0-1.0)
        use_cache: 是否使用缓存文件

    Returns:
        bool: 朗读是否成功
    """
    return tts_manager.speak_text(text, language, volume, use_cache=use_cache)


def install_tts_dependencies() -> bool:
    """安装TTS依赖"""
    return tts_manager.install_dependencies()


def get_tts_info() -> Dict[str, Any]:
    """获取TTS服务信息"""
    return tts_manager.get_service_info()


def get_audio_file_path(text: str, language: str = 'zh-CN', relative: bool = True) -> str:
    """
    获取文本对应的音频文件路径

    Args:
        text: 文本内容
        language: 语言代码
        relative: 是否返回相对路径

    Returns:
        str: 音频文件路径
    """
    return str(tts_manager.get_audio_file_path(text, language, relative))


def list_cached_audio_files(language: str = None) -> Dict[str, list]:
    """
    列出缓存的音频文件

    Args:
        language: 指定语言，如果为None则列出所有语言

    Returns:
        Dict[str, list]: 按语言分组的文件列表
    """
    return tts_manager.list_cached_files(language)


def clear_audio_cache(language: str = None, confirm: bool = False) -> bool:
    """
    清理音频缓存

    Args:
        language: 指定语言，如果为None则清理所有语言
        confirm: 是否确认删除

    Returns:
        bool: 清理是否成功
    """
    return tts_manager.clear_cache(language, confirm)


def get_cache_statistics() -> Dict[str, Any]:
    """
    获取缓存统计信息

    Returns:
        Dict[str, Any]: 缓存统计信息
    """
    return tts_manager.get_cache_stats()


def generate_audio_file(text: str, language: str = 'zh-CN', relative: bool = True) -> str:
    """
    生成音频文件（使用分类存储）

    Args:
        text: 文本内容
        language: 语言代码
        relative: 是否返回相对路径

    Returns:
        str: 生成的音频文件路径，失败返回空字符串
    """
    success = tts_manager.generate_audio_file(text, None, language)
    if success:
        return str(tts_manager.get_audio_file_path(text, language, relative))
    else:
        return ""

if __name__ == '__main__':
    # from tts_utils import speak_text, generate_audio_file

    # 自动分类存储和缓存复用
    # speak_text("open bluetooth", "en-US", use_cache=True)
    # 文件自动保存到: data/en/open_bluetooth.wav

    # 生成持久化文件
    # audio_path = generate_audio_file("open bluetooth", "zh-CN")
    # audio_path = generate_audio_file("open bluetooth", "en-US")
    audio_path = generate_audio_file("close bluetooth", "en-US")
    # 返回: data/zh/打开蓝牙.wav
